version: '3.8'

services:
  postgres:
    image: postgres:13
    container_name: hoams_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: hoams
    ports:
      - '5433:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - hoams_network

  redis:
    image: redis:7-alpine
    container_name: hoams_redis
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
    networks:
      - hoams_network
    command: redis-server --appendonly yes
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  hoams_network:
    driver: bridge
