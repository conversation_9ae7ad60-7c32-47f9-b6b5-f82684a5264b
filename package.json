{"name": "sabino-manager", "version": "1.0.0", "description": "", "author": "<PERSON>", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "db:seed": "ts-node --transpile-only prisma/seeders/base.ts"}, "dependencies": {"@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.17", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^8.0.5", "@prisma/client": "^5.22.0", "@supabase/supabase-js": "^2.49.4", "bcryptjs": "^2.4.3", "bullmq": "^5.56.1", "class-validator": "^0.14.1", "cloudinary": "^1.41.3", "cookie-parser": "^1.4.7", "expo-server-sdk": "^3.15.0", "firebase-admin": "^13.0.0", "ioredis": "^5.6.1", "luxon": "^3.5.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nestjs-pino": "^4.1.0", "newrelic": "^12.6.1", "nodemailer": "^6.9.16", "passport-jwt": "^4.0.1", "pino-pretty": "^12.1.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "uuid": "^11.0.2"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.16", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^5.22.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.{json,css,md}": ["prettier --write"]}}