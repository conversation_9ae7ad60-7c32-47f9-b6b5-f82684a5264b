import { Module } from '@nestjs/common';
import { App<PERSON>ontroller } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './modules/business-logic/user/user.module';
import { DatabaseModule } from './modules/database/database.module';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { LoggerModule } from 'nestjs-pino';
import configuration, { Config } from './config/configuration';
import { CONST } from './config/constants';
import { v4 as uuidv4 } from 'uuid';
import { PaymentModule } from './modules/business-logic/payment/payment.module';
import { RentalModule } from './modules/business-logic/rental/rental.module';
import { ReservationModule } from './modules/business-logic/reservation/reservation.module';
import { FacilityModule } from './modules/business-logic/facility/facility.module';
import { PropertyModule } from './modules/business-logic/property/property.module';
import { RoleModule } from './modules/business-logic/role/role.module';
import { RegulationModule } from './modules/business-logic/regulation/regulation.module';
import { ProtocolModule } from './modules/business-logic/protocol/protocol.module';
import { ParkingSpotModule } from './modules/business-logic/parking-spot/parking-spot.module';
import { VisitModule } from './modules/business-logic/visit/visit.module';
import { NewsModule } from './modules/business-logic/news/news.module';
import { EventModule } from './modules/business-logic/event/event.module';
import { InfractionModule } from './modules/business-logic/infraction/infraction.module';
import { FineModule } from './modules/business-logic/fine/fine.module';
import { NotificationModule } from './modules/notification/notification.module';
import { ServiceModule } from './modules/business-logic/service/service.module';
import { SupplierModule } from './modules/business-logic/supplier/supplier.module';
import { ExpenseModule } from './modules/business-logic/expense/expense.module';
import { TaskModule } from './modules/business-logic/task/task.module';
import { MailModule } from './modules/mail/mail.module';
import { ErrorHandlerModule } from './modules/error-handler/error-handler.module';
import { AuthModule } from './modules/auth/auth.module';
import { EmployeeModule } from './modules/business-logic/employee/employee.module';
import { TagModule } from './modules/business-logic/tag/tag.module';
import { VehicleModule } from './modules/business-logic/vehicle/vehicle.module';
import { PetModule } from './modules/business-logic/pet/pet.module';
import { PhoneDirectoryModule } from './modules/business-logic/phone-directory/phone-directory.module';
import { ComplaintModule } from './modules/business-logic/complaint/complaint.module';
import { ComplaintTypeModule } from './modules/business-logic/complaint-type/complaint-type.module';
import { MaintenanceIssueReportModule } from './modules/business-logic/maintenance-issue-report/maintenance-issue-report.module';
import { AnnouncementModule } from './modules/business-logic/announcement/announcement.module';
import { StepModule } from './modules/business-logic/step/step.module';
import { StorageModule } from './modules/storage/storage.module';
import { MaintenanceFeeModule } from './modules/business-logic/maintenance-fee/maintenance-fee.module';
import { MonthlyMaintenanceChargeModule } from './modules/business-logic/monthly-maintenance-charge/monthly-maintenance-charge.module';
import { PersistenceModule } from './modules/persistence/persistence.module';
import { MobileModule } from './modules/mobile/mobile.module';
import { PackageModule } from './modules/business-logic/package/package.module';
import { PushNotificationsModule } from './modules/business-logic/push-notifications/push-notifications.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    LoggerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const config = configService.get<Config>(CONST.CONFIG_KEY);
        return {
          pinoHttp: {
            serializers: {
              req(req) {
                req.body = req.raw.body;
                return req;
              },
            },
            formatters: {
              level: (label) => {
                return { level: label };
              },
            },
            customReceivedMessage: (req, _res) => {
              return `request received ${req.method} ${req.url}`;
            },
            customSuccessMessage: (res) => {
              if (res.statusCode === 404) {
                return 'resource not found';
              }
              return 'request completed';
            },
            autoLogging: true,
            transport:
              config.environment === CONST.DEVELOPMENT
                ? {
                    target: 'pino-pretty',
                    options: {
                      colorize: true,
                      levelFirst: true,
                      translateTime: 'UTC:mm/dd/yyyy, h:MM:ss TT Z',
                      crlf: true,
                      ignore: config.environment === CONST.DEVELOPMENT ? 'req' : undefined,
                    },
                  }
                : undefined,
            genReqId: () => uuidv4(),
            level: 'debug',
          },
        };
      },
    }),
    DatabaseModule,
    UserModule,
    PaymentModule,
    RentalModule,
    ReservationModule,
    FacilityModule,
    PropertyModule,
    RoleModule,
    RegulationModule,
    ProtocolModule,
    ParkingSpotModule,
    VisitModule,
    NewsModule,
    EventModule,
    InfractionModule,
    FineModule,
    NotificationModule,
    ServiceModule,
    SupplierModule,
    EmployeeModule,
    ExpenseModule,
    TaskModule,
    MailModule,
    ErrorHandlerModule,
    AuthModule,
    TagModule,
    VehicleModule,
    PetModule,
    PhoneDirectoryModule,
    ComplaintModule,
    ComplaintTypeModule,
    MaintenanceIssueReportModule,
    AnnouncementModule,
    StepModule,
    StorageModule,
    MaintenanceFeeModule,
    MonthlyMaintenanceChargeModule,
    PersistenceModule,
    MobileModule,
    PackageModule,
    PushNotificationsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
