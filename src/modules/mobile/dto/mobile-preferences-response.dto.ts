import { ApiProperty } from '@nestjs/swagger';

export class MobilePreferencesResponseDto {
  @ApiProperty({ description: 'ID de las preferencias', example: 'preferences-uuid' })
  id: string;

  @ApiProperty({ description: 'ID del usuario', example: 'user-uuid' })
  userId: string;

  @ApiProperty({ description: 'Modo oscuro activado', example: false })
  darkMode: boolean;

  @ApiProperty({ description: 'Idioma preferido del usuario', example: 'es' })
  language: string;

  @ApiProperty({ 
    description: 'ID de la propiedad preferida del usuario', 
    example: 'property-uuid',
    required: false 
  })
  preferredPropertyId?: string;

  @ApiProperty({ description: 'Recibir notificaciones por email', example: false })
  notificateByEmail: boolean;

  @ApiProperty({ description: 'Fecha de creación', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Fecha de actualización', example: '2023-01-01T00:00:00.000Z' })
  updatedAt: Date;
}
