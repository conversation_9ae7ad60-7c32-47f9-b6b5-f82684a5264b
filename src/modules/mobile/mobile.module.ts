import { Module } from '@nestjs/common';
import { MobileService } from './mobile.service';
import { MobileController } from './mobile.controller';
import { PersistenceModule } from '../persistence/persistence.module';
import { StorageModule } from '../storage/storage.module';
import { PushNotificationsModule } from '../business-logic/push-notifications/push-notifications.module';
import { UserModule } from '../business-logic/user/user.module';
import { ComplaintModule } from '../business-logic/complaint/complaint.module';
import { ReservationModule } from '../business-logic/reservation/reservation.module';
import { MaintenanceIssueReportModule } from '../business-logic/maintenance-issue-report/maintenance-issue-report.module';
import { FacilityModule } from '../business-logic/facility/facility.module';
import { PhoneDirectoryModule } from '../business-logic/phone-directory/phone-directory.module';
import { PropertyModule } from '../business-logic/property/property.module';
import { PackageModule } from '../business-logic/package/package.module';
import { FineModule } from '../business-logic/fine/fine.module';
import { InfractionModule } from '../business-logic/infraction/infraction.module';

@Module({
  imports: [
    PersistenceModule,
    StorageModule,
    PushNotificationsModule,
    UserModule,
    ComplaintModule,
    ReservationModule,
    MaintenanceIssueReportModule,
    FacilityModule,
    PhoneDirectoryModule,
    PropertyModule,
    PackageModule,
    FineModule,
    InfractionModule,
  ],
  controllers: [MobileController],
  providers: [MobileService],
})
export class MobileModule {}
