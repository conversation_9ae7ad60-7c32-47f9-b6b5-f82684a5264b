import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  Patch,
} from '@nestjs/common';
import { MobileService } from './mobile.service';
import { Request } from 'express';
import { CreateMobileComplaintDto } from './dto/create-mobile-complaint.dto';
import { CreateMobileFacilityIssueReportDto } from './dto/create-mobile-facility-report.dto';
import { CreateMobileReservationDto } from './dto/create-mobile-reservation.dto';
import { AuthGuard } from 'src/common/guards/auth/auth.guard';
import { FilesInterceptor } from '@nestjs/platform-express';
import { JwtService } from '@nestjs/jwt';
import { CreatePushTokenDto } from '../business-logic/push-notifications/dto/create-push-token.dto';
import { CreateMobilePreferencesDto } from './dto/create-mobile-preferences.dto';
import { UpdateMobilePreferencesDto } from './dto/update-mobile-preferences.dto';

@UseGuards(AuthGuard)
@Controller('mobile')
export class MobileController {
  constructor(
    private readonly mobileService: MobileService,
    private readonly jwtService: JwtService,
  ) {}

  //User
  @Get('me')
  async findOne(@Req() req: Request) {
    const user = (req as any).user;
    return this.mobileService.findOneForMobile(user.sub);
  }

  @Get('phone-directory')
  async findAllPhoneDirectory() {
    return this.mobileService.findAllPhoneDirectory();
  }

  //Facilities

  @Get('facility/:facilityId')
  async findFacilityById(@Param('facilityId') facilityId: string) {
    return this.mobileService.findFacilityById(facilityId);
  }

  @Get('facilities')
  async findAllFacilites() {
    return this.mobileService.findAllFacilities();
  }

  @Get('facility/:facilityId/reservations')
  async findFacilityReservations(@Param('facilityId') facilityId: string) {
    return this.mobileService.findFacilityReservations(facilityId);
  }

  //Reservations

  @Post('reservation')
  async createReservation(@Body() dto: CreateMobileReservationDto, @Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.createReservation(dto, payload.sub);
  }

  @Get('reservation')
  async findAllReservations(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllReservations(payload.sub);
  }

  @Get('reservation/:reservationId')
  async findReservationById(@Param('reservationId') reservationId: string) {
    return this.mobileService.getReservationById(reservationId);
  }

  //Complaints

  @Get('complaint-types')
  async findAllComplaintTypes() {
    return this.mobileService.findAllComplaintTypes();
  }

  @Get('complaint')
  async findAllComplaints(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllComplaints(payload.sub);
  }

  @Post('complaint')
  @UseInterceptors(FilesInterceptor('files', 3))
  async createComplaint(
    @Body() dto: CreateMobileComplaintDto,
    @Req() req: Request,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.createComplaint(dto, payload.sub, files);
  }

  @Get('complaint/:complaintId')
  async findComplaintById(@Param('complaintId') complaintId: string) {
    return this.mobileService.getComplaintById(complaintId);
  }

  //Fines

  @Get('fine/:fineId')
  async findFineById(@Param('fineId') fineId: string) {
    return this.mobileService.getFineById(fineId);
  }

  //Infractions

  @Get('infraction/:infractionId')
  async findInfractionById(@Param('infractionId') infractionId: string) {
    return this.mobileService.getInfractionById(infractionId);
  }

  //Maintenance Issue Reports
  @Get('maintenance-issue-report')
  async getAllMaintenanceIssueReports(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllMaintenanceIssueReports(payload.sub);
  }

  @Post('maintenance-issue-report')
  @UseInterceptors(FilesInterceptor('files', 3))
  async createMaintenanceIssueReport(
    @Body() dto: CreateMobileFacilityIssueReportDto,
    @Req() req: Request,
    @UploadedFiles() files: Express.Multer.File[],
  ) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.createMaintenanceIssueReport(dto, payload.sub, files);
  }

  @Get('maintenance-issue-report/:maintenanceIssueReportId')
  async findMaintenanceIssueReportById(@Param('maintenanceIssueReportId') maintenanceIssueReportId: string) {
    return this.mobileService.getMaintenanceIssueReportById(maintenanceIssueReportId);
  }

  //Properties
  @Get('property')
  async getAllProperties(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getAllProperties(payload.sub);
  }

  //get residents by propertyid
  @Get('property/:propertyId/residents')
  async getResidentsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getResidentsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/vehicles')
  async getVehiclesByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getVehiclesByPropertyId(propertyId);
  }

  @Get('property/:propertyId/pets')
  async getPetsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getPetsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/tags')
  async getTagsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getTagsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/parking-spots')
  async getParkingSpotsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getParkingSpotsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/reservations')
  async getReservationsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getReservationsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/maintenance-issue-reports')
  async getMaintenanceIssueReportsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getMaintenanceIssueReportsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/infractions')
  async getInfractionsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getInfractionsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/complaints')
  async getComplaintsByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getComplaintsByPropertyId(propertyId);
  }

  @Get('property/:propertyId/fines')
  async getFinesByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getFinesByPropertyId(propertyId);
  }

  @Get('property/:propertyId/monthly-maintenance-charges')
  async getMonthlyMaintenanceChargesByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getMonthlyMaintenanceChargesByPropertyId(propertyId);
  }

  //Packages
  @Get('property/:propertyId/packages')
  async getPackagesByPropertyId(@Param('propertyId') propertyId: string) {
    return this.mobileService.getPackagesByPropertyId(propertyId);
  }

  @Get('package/:packageId')
  async getPackageById(@Param('packageId') packageId: string) {
    return this.mobileService.getPackageById(packageId);
  }

  @Post('package/:packageId/generate-token')
  async generatePackageDeliveryToken(@Param('packageId') packageId: string) {
    return this.mobileService.generatePackageDeliveryToken(packageId);
  }

  //Tokens
  @Post('push-token')
  async registerToken(@Body() createTokenDto: CreatePushTokenDto, @Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.registerToken(createTokenDto, payload.sub);
  }

  @Get('push-token')
  async getMyTokens(@Req() req: Request) {
    const payload = await this.jwtService.verifyAsync(req.cookies.accessToken);
    return this.mobileService.getMyTokens(payload.sub);
  }

  @Delete('push-token/:tokenId')
  async removeToken(@Param('tokenId') tokenId: string) {
    return this.mobileService.removeToken(tokenId);
  }

  // Preferences
  @Get('preferences')
  async getMyPreferences(@Req() req: Request) {
    const user = (req as any).user;
    return this.mobileService.getMyPreferences(user.sub);
  }

  @Post('preferences')
  async createPreferences(@Req() req: Request, @Body() createDto: CreateMobilePreferencesDto) {
    const user = (req as any).user;
    return this.mobileService.createPreferences(user.sub, createDto);
  }

  @Patch('preferences')
  async updatePreferences(@Req() req: Request, @Body() updateDto: UpdateMobilePreferencesDto) {
    const user = (req as any).user;
    return this.mobileService.updatePreferences(user.sub, updateDto);
  }
}
