import { Module } from '@nestjs/common';
import { DatabaseModule } from '../database/database.module';
import { AnnouncementPersistenceService } from './announcement-persistence.service';
import { ComplaintPersistenceService } from './complaint-persistence.service';
import { ComplaintTypePersistenceService } from './complaint-type-persistence.service';
import { EmployeePersistenceService } from './employee-persistence.service';
import { EventPersistenceService } from './event-persistence.service';
import { ExpensePersistenceService } from './expense-persistence.service';
import { FacilityPersistenceService } from './facility-persistence.service';
import { FinePersistenceService } from './fine-persistence.service';
import { InfractionPersistenceService } from './infraction-persistence.service';
import { MaintenanceFeePersistenceService } from './maintenance-fee-persistence.service';
import { MonthlyMaintenanceChargePersistenceService } from './monthly-maintenance-charge-persistence.service';
import { NewsPersistenceService } from './news-persistence.service';
import { ParkingSpotPersistenceService } from './parking-spot-persistence.service';
import { PaymentPersistenceService } from './payment-persistence.service';
import { PetPersistenceService } from './pet-persistence.service';
import { PropertyPersistenceService } from './property-persistence.service';
import { RegulationPersistenceService } from './regulation-persistence.service';
import { RentalPersistenceService } from './rental-persistence.service';
import { ReservationPersistenceService } from './reservation-persistence.service';
import { RolesPersistenceService } from './roles-persistence.service';
import { ServicePersistenceService } from './service-persistence.service';
import { StepPersistenceService } from './step-persistence.service';
import { SupplierPersistenceService } from './supplier-persistence.service';
import { TagPersistenceService } from './tag-persistence.service';
import { TaskPersistenceService } from './task-persistence.service';
import { UserPersistenceService } from './user-persistence.service';
import { VehiclePersistenceService } from './vehicle-persistence.service';
import { VisitPersistenceService } from './visit-persistence.service';
import { ProtocolPersistenceService } from './protocol-persistence.service';
import { MaintenanceIssueReportPersistenceService } from './maintenance-issue-report-persistence.service';
import { PhoneDirectoryPersistenceService } from './phone-directory-persistence.service';
import { PackagePersistenceService } from './package-persistence.service';
import { PushTokenPersistenceService } from './push-token-persistence.service';

@Module({
  imports: [DatabaseModule],
  providers: [
    AnnouncementPersistenceService,
    ComplaintPersistenceService,
    ComplaintTypePersistenceService,
    EmployeePersistenceService,
    EventPersistenceService,
    ExpensePersistenceService,
    MaintenanceIssueReportPersistenceService,
    FacilityPersistenceService,
    FinePersistenceService,
    InfractionPersistenceService,
    MaintenanceFeePersistenceService,
    MonthlyMaintenanceChargePersistenceService,
    NewsPersistenceService,
    ParkingSpotPersistenceService,
    PaymentPersistenceService,
    PetPersistenceService,
    PhoneDirectoryPersistenceService,
    PropertyPersistenceService,
    ProtocolPersistenceService,
    RegulationPersistenceService,
    RentalPersistenceService,
    ReservationPersistenceService,
    RolesPersistenceService,
    ServicePersistenceService,
    StepPersistenceService,
    SupplierPersistenceService,
    TagPersistenceService,
    TaskPersistenceService,
    UserPersistenceService,
    VehiclePersistenceService,
    VisitPersistenceService,
    PackagePersistenceService,
    PushTokenPersistenceService,
  ],
  exports: [
    AnnouncementPersistenceService,
    ComplaintPersistenceService,
    ComplaintTypePersistenceService,
    EmployeePersistenceService,
    EventPersistenceService,
    ExpensePersistenceService,
    MaintenanceIssueReportPersistenceService,
    FacilityPersistenceService,
    FinePersistenceService,
    InfractionPersistenceService,
    MaintenanceFeePersistenceService,
    MonthlyMaintenanceChargePersistenceService,
    NewsPersistenceService,
    ParkingSpotPersistenceService,
    PaymentPersistenceService,
    PetPersistenceService,
    PhoneDirectoryPersistenceService,
    PropertyPersistenceService,
    ProtocolPersistenceService,
    RegulationPersistenceService,
    RentalPersistenceService,
    ReservationPersistenceService,
    RolesPersistenceService,
    ServicePersistenceService,
    StepPersistenceService,
    SupplierPersistenceService,
    TagPersistenceService,
    TaskPersistenceService,
    UserPersistenceService,
    VehiclePersistenceService,
    VisitPersistenceService,
    PackagePersistenceService,
    PushTokenPersistenceService,
  ],
})
export class PersistenceModule {}
