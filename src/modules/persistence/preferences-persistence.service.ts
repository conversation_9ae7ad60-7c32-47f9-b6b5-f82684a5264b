import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreateMobilePreferencesDto } from '../mobile/dto/create-mobile-preferences.dto';
import { UpdateMobilePreferencesDto } from '../mobile/dto/update-mobile-preferences.dto';

@Injectable()
export class PreferencesPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear preferencias para un usuario
  async create(userId: string, data: CreateMobilePreferencesDto) {
    return this.prisma.preferences.create({
      data: {
        userId,
        darkMode: data.darkMode ?? false,
        language: data.language ?? 'es',
        preferredPropertyId: data.preferredPropertyId || null,
        notificateByEmail: data.notificateByEmail ?? false,
      },
    });
  }

  // 🔹 Obtener preferencias por ID de usuario
  async findByUserId(userId: string) {
    return this.prisma.preferences.findUnique({
      where: { userId },
    });
  }

  // 🔹 Obtener preferencias por ID
  async findById(preferencesId: string) {
    return this.prisma.preferences.findUnique({
      where: { id: preferencesId },
    });
  }

  // 🔹 Actualizar preferencias
  async update(userId: string, data: UpdateMobilePreferencesDto) {
    return this.prisma.preferences.update({
      where: { userId },
      data: {
        darkMode: data.darkMode,
        language: data.language,
        preferredPropertyId: data.preferredPropertyId,
        notificateByEmail: data.notificateByEmail,
      },
    });
  }

  // 🔹 Crear o actualizar preferencias (upsert)
  async upsert(userId: string, data: CreateMobilePreferencesDto | UpdateMobilePreferencesDto) {
    return this.prisma.preferences.upsert({
      where: { userId },
      update: {
        darkMode: data.darkMode,
        language: data.language,
        preferredPropertyId: data.preferredPropertyId,
        notificateByEmail: data.notificateByEmail,
      },
      create: {
        userId,
        darkMode: data.darkMode ?? false,
        language: data.language ?? 'es',
        preferredPropertyId: data.preferredPropertyId || null,
        notificateByEmail: data.notificateByEmail ?? false,
      },
    });
  }

  // 🔹 Eliminar preferencias
  async delete(userId: string) {
    return this.prisma.preferences.delete({
      where: { userId },
    });
  }
}
