import { Injectable } from '@nestjs/common';
import { PrismaService } from '../database/prisma/prisma.service';
import { CreatePushTokenDto } from '../business-logic/push-notifications/dto/create-push-token.dto';
import { UpdatePushTokenDto } from '../business-logic/push-notifications/dto/update-push-token.dto';

@Injectable()
export class PushTokenPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear un token push
  async create(data: CreatePushTokenDto, userId: string) {
    return this.prisma.pushToken.create({
      data: {
        token: data.token,
        device: data.device || null,
        userId: userId,
        lastUsed: new Date(),
      },
    });
  }

  // 🔹 Obtener todos los tokens push
  async findAll() {
    return this.prisma.pushToken.findMany({
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            email: true,
          },
        },
      },
    });
  }

  // 🔹 Obtener tokens por usuario
  async findByUserId(userId: string) {
    return this.prisma.pushToken.findMany({
      where: { userId },
      orderBy: { lastUsed: 'desc' },
    });
  }

  // 🔹 Obtener un token por ID
  async findById(tokenId: string) {
    return this.prisma.pushToken.findUnique({
      where: { id: tokenId },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            email: true,
          },
        },
      },
    });
  }

  // 🔹 Obtener un token por valor del token
  async findByToken(token: string) {
    return this.prisma.pushToken.findUnique({
      where: { token },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            email: true,
          },
        },
      },
    });
  }

  // 🔹 Verificar si un token existe
  async tokenExists(token: string): Promise<boolean> {
    const existingToken = await this.prisma.pushToken.findUnique({
      where: { token },
    });
    return !!existingToken;
  }

  // 🔹 Actualizar un token push
  async update(tokenId: string, data: UpdatePushTokenDto) {
    return this.prisma.pushToken.update({
      where: { id: tokenId },
      data: {
        device: data.device,
        lastUsed: new Date(),
      },
    });
  }

  // 🔹 Actualizar último uso del token
  async updateLastUsed(token: string) {
    return this.prisma.pushToken.update({
      where: { token },
      data: {
        lastUsed: new Date(),
      },
    });
  }

  // 🔹 Eliminar un token push por ID
  async delete(tokenId: string) {
    return this.prisma.pushToken.delete({
      where: { id: tokenId },
    });
  }

  // 🔹 Eliminar un token push por valor del token
  async deleteByToken(token: string) {
    return this.prisma.pushToken.delete({
      where: { token },
    });
  }

  // 🔹 Eliminar tokens por usuario
  async deleteByUserId(userId: string) {
    return this.prisma.pushToken.deleteMany({
      where: { userId },
    });
  }

  // 🔹 Eliminar tokens antiguos (más de X días sin usar)
  async deleteOldTokens(daysOld: number = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    return this.prisma.pushToken.deleteMany({
      where: {
        lastUsed: {
          lt: cutoffDate,
        },
      },
    });
  }

  // 🔹 Obtener tokens activos de usuarios específicos
  async findActiveTokensByUserIds(userIds: string[]) {
    return this.prisma.pushToken.findMany({
      where: {
        userId: {
          in: userIds,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            email: true,
          },
        },
      },
    });
  }

  // 🔹 Obtener estadísticas de tokens
  async getTokenStats() {
    const totalTokens = await this.prisma.pushToken.count();
    const uniqueUsers = await this.prisma.pushToken.groupBy({
      by: ['userId'],
    });

    const deviceStats = await this.prisma.pushToken.groupBy({
      by: ['device'],
      _count: {
        device: true,
      },
    });

    return {
      totalTokens,
      uniqueUsers: uniqueUsers.length,
      deviceStats: deviceStats.map((stat) => ({
        device: stat.device || 'Unknown',
        count: stat._count.device,
      })),
    };
  }

  //Get the firs reccord if exist
  async getFirstRecord() {
    return this.prisma.pushToken.findFirst();
  }
}
