import { Injectable } from '@nestjs/common';
import { PackageStatus, Property, ReservationStatus, Status } from '@prisma/client';
import { PropertySelectDto } from '../business-logic/property/dto/property-select.dto';
import { PrismaService } from 'src/modules/database/prisma/prisma.service';
import { CreatePropertyDto } from '../business-logic/property/dto/create-property.dto';
import { PropertyResponseDto } from '../business-logic/property/dto/response-property.dto';
import { UpdatePropertyDto } from '../business-logic/property/dto/update-property.dto';
import { GetMobilePropertiesDto } from '../mobile/dto/get-mobile-properties.dto';

@Injectable()
export class PropertyPersistenceService {
  constructor(private readonly prisma: PrismaService) {}

  // 🔹 Crear Propiedad
  async create(data: CreatePropertyDto) {
    return this.prisma.property.create({
      data: {
        address: data.address,
        ownerId: data.ownerId || null,
        type: data.type,
        pets: {
          create: data.pets || [],
        },
        vehicles: {
          create: data.vehicles || [],
        },
        tags: {
          create: data.tags || [],
        },
        residents: {
          connect: data.residents?.map((residentId) => ({ id: residentId })) || [],
        },
      },
    });
  }

  // 🔹 Obtener una Propiedad por ID
  async findById(id: Property['id']): Promise<PropertyResponseDto | null> {
    return await this.prisma.property.findUnique({
      where: { id },
      include: {
        residents: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
            email: true,
            phone: true,
            roles: true,
          },
        },
        reservations: true,
        maintenanceIssueReports: true,
        rentals: true,
        infractions: true,
        payments: true,
        visits: true,
        parkingSpots: true,
        pets: true,
        vehicles: true,
        tags: true,
        fines: true,
        complaints: true,
      },
    });
  }

  // 🔹 Obtener todas las Propiedades
  async findAll(): Promise<PropertyResponseDto[]> {
    return await this.prisma.property.findMany({
      include: {
        residents: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
            email: true,
            phone: true,
            roles: true,
          },
        },
        infractions: true,
        vehicles: true,
        visits: true,
        maintenanceIssueReports: true, // ✅ Relación agregada
        tags: true,
        parkingSpots: true,
      },
    });
  }

  async findAllForSelect(): Promise<PropertySelectDto[]> {
    return await this.prisma.property.findMany({
      select: {
        id: true,
        address: true,
      },
    });
  }

  // 🔹 Actualizar una propiedad
  async update(propertyId: string, data: UpdatePropertyDto) {
    return this.prisma.property.update({
      where: { id: propertyId },
      data: {
        address: data.address || undefined,
        type: data.type,
        pets: data.pets
          ? {
              deleteMany: {}, // Eliminar todas las mascotas relacionadas
              create: data.pets, // Crear nuevas mascotas
            }
          : undefined,

        vehicles: data.vehicles
          ? {
              deleteMany: {}, // Eliminar todos los vehículos relacionados
              create: data.vehicles, // Crear nuevos vehículos
            }
          : undefined,

        tags: data.tags
          ? {
              deleteMany: {}, // Eliminar todos los tags relacionados
              create: data.tags, // Crear nuevos tags
            }
          : undefined,

        residents: data.residents
          ? {
              set: data.residents.map((residentId) => ({ id: residentId })), // Reemplazar residentes existentes con nuevos
            }
          : undefined,
      },
    });
  }

  // 🔹 Eliminar una propiedad
  async delete(propertyId: string) {
    return this.prisma.property.delete({
      where: { id: propertyId },
    });
  }

  async findForMobileByUserId(userId: string): Promise<GetMobilePropertiesDto[]> {
    return this.prisma.property.findMany({
      where: { residents: { some: { id: userId } } },
      select: {
        id: true,
        address: true,
        type: true,
        _count: {
          select: {
            residents: true,
            parkingSpots: true,
            pets: true,
            vehicles: true,
            tags: true,
          },
        },
        reservations: {
          where: {
            startDateTime: {
              gte: new Date(),
            },
            status: {
              in: [ReservationStatus.PENDING, ReservationStatus.APPROVED, ReservationStatus.REJECTED],
            },
          },
          orderBy: {
            startDateTime: 'asc',
          },
          //Get all the data and select facility name
          select: {
            id: true,
            status: true,
            startDateTime: true,
            endDateTime: true,
            amountOfPeople: true,
            createdAt: true,
            facility: {
              select: {
                name: true,
              },
            },
          },
        },
        fines: {
          where: {
            isPaid: false,
          },
          orderBy: {
            issuedAt: 'desc',
          },
          select: {
            id: true,
            amount: true,
            paidAt: true,
            description: true,
            issuedAt: true,
            isPaid: true,
          },
        },
        complaints: {
          where: {
            status: { in: [Status.OPEN, Status.IN_PROGRESS, Status.BLOCKED] },
          },
          orderBy: {
            createdAt: 'desc',
          },
          select: {
            id: true,
            detail: true,
            priority: true,
            status: true,
            createdAt: true,
            complaintType: {
              select: {
                name: true,
                description: true,
              },
            },
          },
        },
        maintenanceIssueReports: {
          where: {
            status: {
              in: [Status.OPEN, Status.IN_PROGRESS, Status.BLOCKED],
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          select: {
            id: true,
            description: true,
            status: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        infractions: {
          where: {
            date: {
              gte: new Date(new Date().setMonth(new Date().getMonth() - 1)),
            },
          },
          orderBy: {
            date: 'desc',
          },
          select: {
            id: true,
            date: true,
            severity: true,
            description: true,
          },
        },
        monthlyMaintenanceCharges: {
          where: {
            isPaid: false,
          },
          orderBy: {
            dueDate: 'desc',
          },
          select: {
            id: true,
            dueDate: true,
            isPaid: true,
          },
        },
        packages: {
          where: {
            status: {
              in: [PackageStatus.PENDING, PackageStatus.DELIVERED, PackageStatus.RETURNED],
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          select: {
            id: true,
            number: true,
            status: true,
            receivedAt: true,
            deliveredAt: true,
            notes: true,
          },
        },
      },
    });
  }

  async getResidentsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        residents: {
          select: {
            id: true,
            firstName: true,
            paternalLastName: true,
            maternalLastName: true,
            email: true,
            phone: true,
            roles: true,
          },
        },
      },
    });
  }

  async getVehiclesByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        vehicles: {
          select: {
            id: true,
            plate: true,
            brand: true,
            model: true,
            color: true,
          },
        },
      },
    });
  }

  async getPetsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        pets: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });
  }

  async getTagsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        tags: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }

  async getParkingSpotsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        parkingSpots: {
          select: {
            id: true,
            type: true,
          },
        },
      },
    });
  }

  async getReservationsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        reservations: {
          where: {
            startDateTime: {
              gte: new Date(),
            },
            status: {
              in: [ReservationStatus.PENDING, ReservationStatus.APPROVED],
            },
          },
          orderBy: {
            startDateTime: 'asc',
          },
          select: {
            id: true,
            status: true,
            startDateTime: true,
            facility: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });
  }

  async getMaintenanceIssueReportsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        maintenanceIssueReports: {
          where: {
            status: {
              in: [Status.OPEN, Status.IN_PROGRESS, Status.BLOCKED],
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          select: {
            id: true,
            description: true,
            status: true,
            createdAt: true,
          },
        },
      },
    });
  }

  async getInfractionsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        infractions: {
          where: {
            date: {
              gte: new Date(new Date().setMonth(new Date().getMonth() - 1)),
            },
          },
          orderBy: {
            date: 'desc',
          },
          select: {
            id: true,
            date: true,
            severity: true,
            description: true,
          },
        },
      },
    });
  }

  async getComplaintsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        complaints: {
          where: {
            status: {
              in: [Status.OPEN, Status.IN_PROGRESS, Status.BLOCKED],
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
          select: {
            id: true,
            detail: true,
            priority: true,
            status: true,
          },
        },
      },
    });
  }

  async getFinesByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        fines: {
          where: {
            isPaid: false,
          },
          orderBy: {
            issuedAt: 'desc',
          },
          select: {
            id: true,
            amount: true,
            paidAt: true,
          },
        },
      },
    });
  }

  async getMonthlyMaintenanceChargesByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        monthlyMaintenanceCharges: {
          where: {
            isPaid: false,
          },
          orderBy: {
            dueDate: 'desc',
          },
          select: {
            id: true,
            dueDate: true,
            isPaid: true,
          },
        },
      },
    });
  }

  async getPropertyResidentsIdsByPropertyId(propertyId: string) {
    return this.prisma.property.findUnique({
      where: { id: propertyId },
      select: {
        residents: {
          select: {
            id: true,
          },
        },
      },
    });
  }
}
