import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcryptjs';
import { UserService } from '../business-logic/user/user.service';
import { AuthInput, AuthResult, UserWithRoles } from './auth.types';
import { Response, Request } from 'express';
import { ConfigType } from '@nestjs/config';
import configuration from 'src/config/configuration';
import { CONST } from '../../config/constants';
import { ConfirmPasswordDto } from './dto/confir-password.dto';
import { ValidateTokenEmailDto } from './dto/validations.dto';

@Injectable()
export class AuthService {
  constructor(
    @Inject(configuration.KEY)
    private readonly config: ConfigType<typeof configuration>,
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
  ) {}

  async authenticate(authInput: AuthInput, res: Response): Promise<AuthResult> {
    const user = await this.userService.findByEmail(authInput.email);

    const validUser =
      user && user.passwordConfirmed && (await bcrypt.compare(authInput.password, user.password)) ? user : null;

    if (validUser === null) {
      throw new UnauthorizedException();
    }

    return this.signIn(user, res);
  }

  async validateUser(authInput: AuthInput) {
    const { email, password } = authInput;
    const user = await this.userService.findByEmail(email);
    return user && user.passwordConfirmed && (await bcrypt.compare(password, user.password)) ? user : null;
  }

  async signIn(user: UserWithRoles, res: Response): Promise<AuthResult> {
    const { id, email, firstName, paternalLastName, maternalLastName, roles } = user;

    const rolesName = roles.map((role) => role.name);

    const accessTokenPayload = {
      sub: id,
      username: email,
      roles: rolesName,
    };
    const refreshTokenPayload = { sub: id };

    const accessToken = await this.jwtService.signAsync(accessTokenPayload, { expiresIn: '15m' });

    const refreshToken = await this.jwtService.signAsync(refreshTokenPayload, { expiresIn: '7d' });

    await this.storeRefreshToken(user.id, refreshToken);

    res.cookie('accessToken', accessToken, {
      httpOnly: true, // No accesible mediante JavaScript
      secure: true, // Solo se envía en HTTPS
      sameSite: 'none', // Protege contra CSRF
      maxAge: 15 * 60 * 1000, // 1 hora de duración
    });

    res.cookie('refreshToken', refreshToken, {
      httpOnly: true,
      secure: true,
      sameSite: 'none',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 días de duración
    });

    return { firstName, paternalLastName, maternalLastName, roles: rolesName };
  }

  async logout(res: Response): Promise<{ message: string }> {
    res.clearCookie('accessToken', {
      httpOnly: true,
      secure: this.config.environment === CONST.PRODUCTION,
      sameSite: 'none',
    });

    res.clearCookie('refreshToken', {
      httpOnly: true,
      secure: this.config.environment === CONST.PRODUCTION,
      sameSite: 'none',
    });

    return { message: 'Logout successful' };
  }

  async validateJWTToken(req: Request) {
    console.log(req.cookies['accessToken']);
    const token = req.cookies['accessToken'];

    if (!token) {
      throw new UnauthorizedException('No access token provided');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token);

      return { authenticated: true, user: payload };
    } catch (error) {
      return { authenticated: false, message: 'Invalid or expired token' };
    }
  }

  async refreshToken(req: Request, res: Response): Promise<{ message: string }> {
    const refreshToken = req.cookies['refreshToken'];

    if (!refreshToken) {
      throw new UnauthorizedException('No access token provided');
    }

    try {
      const payload = await this.jwtService.verifyAsync(refreshToken);
      const newAccessToken = await this.generateAccessToken(payload.sub);
      res.cookie('accessToken', newAccessToken, {
        httpOnly: true,
        secure: true,
        sameSite: 'none',
        maxAge: 15 * 60 * 1000,
      });
      return { message: 'Refresh token success' };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async generateAccessToken(userId: string) {
    const accessTokenPayload = { sub: userId };
    const newAccessToken = this.jwtService.sign(accessTokenPayload, {
      expiresIn: '15m',
    });

    return newAccessToken;
  }

  async storeRefreshToken(userId: string, refreshToken: string) {
    console.log(userId);
    console.log(refreshToken);
    //
  }

  async confirmPassword(confirmPasswordDto: ConfirmPasswordDto) {
    const { token, password, email } = confirmPasswordDto;
    const user = await this.userService.getUserByTokenAndEmail({ token, email });

    if (!user) {
      throw new Error('Invalid token');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    await this.userService.savePassword(user.id, hashedPassword);

    return { message: 'Password confirmed successfully' };
  }

  async validateEmailAndSendUserToken(email: string) {
    return this.userService.validateEmailAndSendUserToken(email);
  }

  async validateToken(validateTokenAndEmailDto: ValidateTokenEmailDto) {
    return this.userService.validateToken(validateTokenAndEmailDto);
  }
}
