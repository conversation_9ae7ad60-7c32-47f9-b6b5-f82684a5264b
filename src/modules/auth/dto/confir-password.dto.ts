import { User } from '@prisma/client';
import { IsE<PERSON>, IsNotEmpty, IsString } from 'class-validator';
import { Match } from 'src/common/validators/match.validator';

export class ConfirmPasswordDto {
  @IsNotEmpty()
  @IsString()
  token: User['passwordConfirmationToken'];
  @IsNotEmpty()
  @IsEmail()
  email: User['email'];
  @IsNotEmpty()
  @IsString()
  password: User['password'];
  @IsNotEmpty()
  @IsString()
  @Match('password', { message: 'Las contraseñas no coinciden' })
  passwordConfirmation: User['password'];
}
