import { Body, Controller, Get, Post, Req, Res } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthInput } from './auth.types';
import { Response, Request } from 'express';
import { ConfirmPasswordDto } from './dto/confir-password.dto';
import { ValidateEmailDto, ValidateTokenEmailDto } from './dto/validations.dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post()
  async authenticate(@Body() authInput: AuthInput, @Res() res: Response): Promise<Response> {
    const result = await this.authService.authenticate(authInput, res);
    return res.send(result);
  }

  @Post('logout')
  async logout(@Res() res: Response): Promise<Response> {
    const result = await this.authService.logout(res);
    return res.send(result);
  }

  @Get('check')
  async checkAuth(@Req() req: Request) {
    return this.authService.validateJWTToken(req);
  }

  @Post('refresh-token')
  async refreshToken(@Req() req: Request, @Res() res: Response) {
    const result = await this.authService.refreshToken(req, res);
    return res.send(result);
  }

  @Post('confirm-password')
  async confirmPassword(@Body() confirmPasswordDto: ConfirmPasswordDto) {
    return this.authService.confirmPassword(confirmPasswordDto);
  }

  @Post('validate-email')
  async validateEmail(@Body() validateEmailDto: ValidateEmailDto) {
    return this.authService.validateEmailAndSendUserToken(validateEmailDto.email);
  }

  @Post('validate-token')
  async validateTokenAndEmail(@Body() validateTokenAndEmailDto: ValidateTokenEmailDto) {
    return this.authService.validateToken(validateTokenAndEmailDto);
  }
}
