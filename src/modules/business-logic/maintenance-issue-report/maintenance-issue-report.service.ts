import { Injectable } from '@nestjs/common';
import { randomUUID } from 'crypto';
import { CreateMaintenanceIssueReportDto } from './dto/create-maintenance-issue-report.dto';
import { UpdateMaintenanceIssueReportDto } from './dto/update-maintenance-issue-report.dto';
import { MaintenanceIssueReportPersistenceService } from 'src/modules/persistence/maintenance-issue-report-persistence.service';
import { SupabaseService } from 'src/modules/storage/supabase.service';

@Injectable()
export class MaintenanceIssueReportService {
  constructor(
    private readonly maintenanceIssueReportPersistence: MaintenanceIssueReportPersistenceService,
    private readonly supabaseService: SupabaseService,
  ) {}

  async createMaintenanceIssueReport(data: CreateMaintenanceIssueReportDto, files: Express.Multer.File[]) {
    const report = await this.maintenanceIssueReportPersistence.createMaintenanceIssueReport(data);
    if (files?.length > 0) {
      files.forEach(async (file, index) => {
        const extension = file.originalname.split('.').pop();
        const path = `maintenance-report/${report.id}-${index + 1}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.maintenanceIssueReportPersistence.saveImagePath(report.id, uploadedPath);
      });
    }

    return report;
  }

  async findAllMaintenanceIssueReports() {
    const reports = await this.maintenanceIssueReportPersistence.findAllMaintenanceIssueReports();

    for (const report of reports) {
      if (report.images.length > 0) {
        report.images = await Promise.all(
          report.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }

    return reports;
  }

  async getMaintenanceIssueReportById(issueId: string) {
    const maintenanceIssueReport = await this.maintenanceIssueReportPersistence.getMaintenanceIssueReportById(issueId);

    if (maintenanceIssueReport.images.length > 0) {
      maintenanceIssueReport.images = await Promise.all(
        maintenanceIssueReport.images.map(async (image) => {
          const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
          return {
            ...image,
            path: signedUrl,
          };
        }),
      );
    }
    return maintenanceIssueReport;
  }

  async updateMaintenanceIssueReport(
    issueId: string,
    data: UpdateMaintenanceIssueReportDto,
    files: Express.Multer.File[] = [],
  ) {
    const updated = await this.maintenanceIssueReportPersistence.updateMaintenanceIssueReport(issueId, data);

    try {
      const parsedImages: { id: string; path: string }[] =
        typeof data.images === 'string' ? JSON.parse(data.images) : (data.images ?? []);

      const imagesToKeep = new Set(parsedImages.map((img) => img.id));
      const current = await this.maintenanceIssueReportPersistence.getMaintenanceIssueReportById(issueId);

      const imagesToDelete = current.images.filter((img) => !imagesToKeep.has(img.id));

      for (const image of imagesToDelete) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
        await this.maintenanceIssueReportPersistence.deleteImageById(image.id);
      }

      for (const file of files) {
        const extension = file.originalname.split('.').pop();
        const path = `maintenance-report/${updated.id}-${randomUUID()}.${extension}`;

        const uploadedPath = await this.supabaseService.uploadFile('sabino-zibata', path, file.buffer, file.mimetype);

        await this.maintenanceIssueReportPersistence.saveImagePath(updated.id, uploadedPath);
      }

      return updated;
    } catch (error: any) {
      throw new Error(`Error updating maintenance issue report: ${error.message}`);
    }
  }
  async deleteMaintenanceIssueReport(issueId: string) {
    const maintenance = await this.maintenanceIssueReportPersistence.getMaintenanceIssueReportById(issueId);
    const images = maintenance.images;
    if (images.length > 0) {
      for (const image of images) {
        await this.supabaseService.deleteFile('sabino-zibata', image.path);
      }
    }
    return this.maintenanceIssueReportPersistence.deleteMaintenanceIssueReport(issueId);
  }

  async getMaintenanceIssueReportsByUserId(userId: string) {
    const reports = await this.maintenanceIssueReportPersistence.getMaintenanceIssueReportsByUserId(userId);
    for (const report of reports) {
      if (report.images.length > 0) {
        report.images = await Promise.all(
          report.images.map(async (image) => {
            const signedUrl = await this.supabaseService.getSignedUrl('sabino-zibata', image.path, 60 * 60 * 24);
            return {
              ...image,
              path: signedUrl,
            };
          }),
        );
      }
    }
    return reports;
  }
}
