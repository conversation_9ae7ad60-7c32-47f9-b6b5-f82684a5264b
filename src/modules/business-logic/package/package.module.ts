import { Module } from '@nestjs/common';
import { PackageService } from './package.service';
import { PackageController } from './package.controller';
import { PersistenceModule } from 'src/modules/persistence/persistence.module';
import { StorageModule } from 'src/modules/storage/storage.module';
import { PushNotificationsModule } from '../push-notifications/push-notifications.module';
import { PropertyModule } from '../property/property.module';

@Module({
  imports: [PersistenceModule, StorageModule, PushNotificationsModule, PropertyModule],
  controllers: [PackageController],
  providers: [PackageService],
  exports: [PackageService],
})
export class PackageModule {}
