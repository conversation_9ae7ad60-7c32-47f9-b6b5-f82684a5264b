// 🔹 Interfaces para los jobs de notificaciones push con BullMQ

export interface BaseNotificationJobData {
  title: string;
  body: string;
  data?: Record<string, any>;
  priority?: number;
  delay?: number;
  attempts?: number;
}

export interface SingleNotificationJobData extends BaseNotificationJobData {
  type: 'single';
  token?: string;
  userId?: string;
  userIds?: string[];
}

export interface BulkNotificationJobData extends BaseNotificationJobData {
  type: 'bulk';
  userIds: string[];
  batchSize?: number;
}

export interface CleanupJobData {
  type: 'cleanup';
  daysOld: number;
}

export interface TokenValidationJobData {
  type: 'token-validation';
  tokens: string[];
}

export type NotificationJobData = 
  | SingleNotificationJobData 
  | BulkNotificationJobData 
  | CleanupJobData 
  | TokenValidationJobData;

// 🔹 Interfaces para resultados de jobs
export interface NotificationJobResult {
  success: boolean;
  sentCount: number;
  failedCount: number;
  removedTokens?: string[];
  errors?: Array<{ token: string; error: string }>;
  message: string;
  processedAt: Date;
}

export interface CleanupJobResult {
  success: boolean;
  deletedCount: number;
  message: string;
  processedAt: Date;
}

export interface TokenValidationJobResult {
  success: boolean;
  validTokens: string[];
  invalidTokens: string[];
  removedCount: number;
  message: string;
  processedAt: Date;
}

// 🔹 Configuración de colas
export interface QueueConfig {
  name: string;
  defaultJobOptions: {
    removeOnComplete: number;
    removeOnFail: number;
    attempts: number;
    backoff: {
      type: 'exponential';
      delay: number;
    };
  };
}

// 🔹 Estados de jobs
export enum JobStatus {
  WAITING = 'waiting',
  ACTIVE = 'active',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DELAYED = 'delayed',
  PAUSED = 'paused',
}

// 🔹 Prioridades de jobs
export enum JobPriority {
  LOW = 1,
  NORMAL = 5,
  HIGH = 10,
  CRITICAL = 20,
}
