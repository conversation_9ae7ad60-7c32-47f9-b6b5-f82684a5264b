import { Injectable, OnModuleInit } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationQueueService } from './notification-queue.service';

@Injectable()
export class QueueMonitoringService implements OnModuleInit {
  constructor(
    private readonly notificationQueue: NotificationQueueService,
    private readonly logger: Logger,
  ) {}

  async onModuleInit() {
    this.logger.log('Servicio de monitoreo de colas inicializado');
    
    // Verificar estado inicial de la cola
    await this.checkQueueHealth();
  }

  // 🔹 Verificar salud de la cola cada 5 minutos
  @Cron(CronExpression.EVERY_5_MINUTES)
  async checkQueueHealth() {
    try {
      const stats = await this.notificationQueue.getQueueStats();
      
      // Log estadísticas básicas
      this.logger.log('Estadísticas de cola de notificaciones', {
        queueName: stats.queueName,
        waiting: stats.counts.waiting,
        active: stats.counts.active,
        completed: stats.counts.completed,
        failed: stats.counts.failed,
        delayed: stats.counts.delayed,
        isReady: stats.health.isReady,
        isPaused: stats.health.isPaused,
      });

      // Alertas por alto número de jobs fallidos
      if (stats.counts.failed > 50) {
        this.logger.warn(`Alto número de jobs fallidos: ${stats.counts.failed}`, {
          queueName: stats.queueName,
        });
      }

      // Alertas por cola pausada
      if (stats.health.isPaused) {
        this.logger.warn(`Cola pausada: ${stats.queueName}`);
      }

      // Alertas por cola no lista
      if (!stats.health.isReady) {
        this.logger.error(`Cola no está lista: ${stats.queueName}`);
      }

      // Alertas por acumulación de jobs
      if (stats.counts.waiting > 1000) {
        this.logger.warn(`Gran acumulación de jobs en espera: ${stats.counts.waiting}`, {
          queueName: stats.queueName,
        });
      }

    } catch (error: any) {
      this.logger.error(`Error verificando salud de la cola: ${error.message}`);
    }
  }

  // 🔹 Limpiar jobs antiguos cada hora
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupOldJobs() {
    try {
      await this.notificationQueue.cleanQueue();
      this.logger.log('Limpieza automática de jobs completada');
    } catch (error: any) {
      this.logger.error(`Error en limpieza automática de jobs: ${error.message}`);
    }
  }

  // 🔹 Obtener métricas detalladas de la cola
  async getDetailedMetrics() {
    try {
      const stats = await this.notificationQueue.getQueueStats();
      const queue = this.notificationQueue.queue;

      // Obtener jobs recientes para análisis
      const recentCompleted = await queue.getCompleted(0, 9);
      const recentFailed = await queue.getFailed(0, 9);
      const waiting = await queue.getWaiting(0, 9);
      const active = await queue.getActive(0, 9);

      // Calcular métricas de rendimiento
      const completedJobs = recentCompleted.length;
      const failedJobs = recentFailed.length;
      const successRate = completedJobs > 0 ? 
        (completedJobs / (completedJobs + failedJobs)) * 100 : 0;

      // Calcular tiempo promedio de procesamiento
      const avgProcessingTime = recentCompleted.length > 0 ?
        recentCompleted.reduce((sum, job) => {
          const processedOn = job.processedOn || 0;
          const timestamp = job.timestamp;
          return sum + (processedOn - timestamp);
        }, 0) / recentCompleted.length : 0;

      return {
        ...stats,
        metrics: {
          successRate: Math.round(successRate * 100) / 100,
          avgProcessingTimeMs: Math.round(avgProcessingTime),
          recentJobs: {
            completed: completedJobs,
            failed: failedJobs,
            waiting: waiting.length,
            active: active.length,
          },
        },
        recentErrors: recentFailed.map(job => ({
          id: job.id,
          name: job.name,
          error: job.failedReason,
          timestamp: new Date(job.timestamp),
          attempts: job.attemptsMade,
        })),
      };
    } catch (error: any) {
      this.logger.error(`Error obteniendo métricas detalladas: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Reintentar jobs fallidos específicos
  async retryFailedJobs(jobIds?: string[]) {
    try {
      const queue = this.notificationQueue.queue;
      let retriedCount = 0;

      if (jobIds && jobIds.length > 0) {
        // Reintentar jobs específicos
        for (const jobId of jobIds) {
          const job = await queue.getJob(jobId);
          if (job && job.isFailed()) {
            await job.retry();
            retriedCount++;
            this.logger.log(`Job ${jobId} reintentado manualmente`);
          }
        }
      } else {
        // Reintentar todos los jobs fallidos recientes
        const failedJobs = await queue.getFailed(0, 50);
        for (const job of failedJobs) {
          if (job.attemptsMade < 3) { // Solo reintentar si no ha excedido el límite
            await job.retry();
            retriedCount++;
          }
        }
      }

      this.logger.log(`${retriedCount} jobs reintentados manualmente`);
      return { retriedCount };
    } catch (error: any) {
      this.logger.error(`Error reintentando jobs fallidos: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Pausar procesamiento de la cola
  async pauseProcessing() {
    try {
      await this.notificationQueue.pauseQueue();
      this.logger.log('Procesamiento de cola pausado manualmente');
    } catch (error: any) {
      this.logger.error(`Error pausando cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Reanudar procesamiento de la cola
  async resumeProcessing() {
    try {
      await this.notificationQueue.resumeQueue();
      this.logger.log('Procesamiento de cola reanudado manualmente');
    } catch (error: any) {
      this.logger.error(`Error reanudando cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Obtener información de un job específico
  async getJobInfo(jobId: string) {
    try {
      const job = await this.notificationQueue.getJob(jobId);
      if (!job) {
        return null;
      }

      return {
        id: job.id,
        name: job.name,
        data: job.data,
        opts: job.opts,
        progress: job.progress,
        delay: job.delay,
        timestamp: new Date(job.timestamp),
        processedOn: job.processedOn ? new Date(job.processedOn) : null,
        finishedOn: job.finishedOn ? new Date(job.finishedOn) : null,
        attemptsMade: job.attemptsMade,
        failedReason: job.failedReason,
        returnvalue: job.returnvalue,
        isCompleted: await job.isCompleted(),
        isFailed: await job.isFailed(),
        isActive: await job.isActive(),
        isWaiting: await job.isWaiting(),
        isDelayed: await job.isDelayed(),
      };
    } catch (error: any) {
      this.logger.error(`Error obteniendo información del job ${jobId}: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Eliminar jobs específicos
  async removeJobs(jobIds: string[]) {
    try {
      let removedCount = 0;
      
      for (const jobId of jobIds) {
        const job = await this.notificationQueue.getJob(jobId);
        if (job) {
          await job.remove();
          removedCount++;
          this.logger.log(`Job ${jobId} eliminado manualmente`);
        }
      }

      return { removedCount };
    } catch (error: any) {
      this.logger.error(`Error eliminando jobs: ${error.message}`);
      throw error;
    }
  }
}
