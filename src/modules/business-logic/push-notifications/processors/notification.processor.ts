import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { Job } from 'bullmq';
import { Expo, ExpoPushMessage, ExpoPushTicket } from 'expo-server-sdk';
import { PushTokenPersistenceService } from '../../../persistence/push-token-persistence.service';
import {
  NotificationJobData,
  SingleNotificationJobData,
  BulkNotificationJobData,
  CleanupJobData,
  NotificationJobResult,
  CleanupJobResult,
} from '../interfaces/notification-job.interface';

@Injectable()
@Processor('push-notifications')
export class NotificationProcessor extends WorkerHost {
  private readonly expo: Expo;

  constructor(
    private readonly pushTokenPersistence: PushTokenPersistenceService,
    private readonly logger: Logger,
  ) {
    super();

    // Inicializar Expo SDK
    this.expo = new Expo({
      accessToken: process.env.EXPO_ACCESS_TOKEN,
      useFcmV1: true,
    });
  }

  async process(job: Job<NotificationJobData>): Promise<any> {
    const { data } = job;

    try {
      this.logger.log(`Procesando job ${job.id} de tipo ${data.type}`);

      switch (data.type) {
        case 'single':
          return await this.processSingleNotification(job, data);
        case 'bulk':
          return await this.processBulkNotification(job, data);
        case 'cleanup':
          return await this.processCleanup(job, data);
        default:
          throw new Error(`Tipo de job no soportado: ${(data as any).type}`);
      }
    } catch (error: any) {
      this.logger.error(`Error procesando job ${job.id}: ${error.message}`);
      throw error;
    }
  }

  private async processSingleNotification(
    job: Job<NotificationJobData>,
    data: SingleNotificationJobData,
  ): Promise<NotificationJobResult> {
    try {
      let tokens: string[] = [];

      // Obtener tokens según los parámetros
      if (data.token) {
        tokens = [data.token];
      } else if (data.userId) {
        const userTokens = await this.pushTokenPersistence.findByUserId(data.userId);
        tokens = userTokens.map((t) => t.token);
      } else if (data.userIds && data.userIds.length > 0) {
        const userTokens = await this.pushTokenPersistence.findActiveTokensByUserIds(data.userIds);
        tokens = userTokens.map((t) => t.token);
      }

      if (tokens.length === 0) {
        return {
          success: false,
          sentCount: 0,
          failedCount: 0,
          message: 'No se encontraron tokens válidos',
          processedAt: new Date(),
        };
      }

      // Actualizar progreso
      await job.updateProgress(25);

      // Procesar notificaciones
      const result = await this.sendPushNotifications(tokens, data.title, data.body, data.data);

      // Actualizar progreso
      await job.updateProgress(100);

      return {
        success: result.sentCount > 0,
        sentCount: result.sentCount,
        failedCount: result.failedCount,
        removedTokens: result.removedTokens,
        errors: result.errors,
        message: `Notificación procesada: ${result.sentCount} enviadas, ${result.failedCount} fallidas`,
        processedAt: new Date(),
      };
    } catch (error: any) {
      this.logger.error(`Error en procesamiento de notificación individual: ${error.message}`);
      throw error;
    }
  }

  private async processBulkNotification(
    job: Job<NotificationJobData>,
    data: BulkNotificationJobData,
  ): Promise<NotificationJobResult> {
    try {
      const batchSize = data.batchSize || 100;
      const userIds = data.userIds;
      let totalSent = 0;
      let totalFailed = 0;
      let allRemovedTokens: string[] = [];
      let allErrors: Array<{ token: string; error: string }> = [];

      // Procesar en lotes
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize);

        // Actualizar progreso
        const progress = Math.round((i / userIds.length) * 100);
        await job.updateProgress(progress);

        // Obtener tokens del lote
        const userTokens = await this.pushTokenPersistence.findActiveTokensByUserIds(batch);
        const tokens = userTokens.map((t) => t.token);

        if (tokens.length > 0) {
          const result = await this.sendPushNotifications(tokens, data.title, data.body, data.data);
          totalSent += result.sentCount;
          totalFailed += result.failedCount;

          if (result.removedTokens) {
            allRemovedTokens.push(...result.removedTokens);
          }
          if (result.errors) {
            allErrors.push(...result.errors);
          }
        }

        // Pequeña pausa entre lotes para no sobrecargar
        if (i + batchSize < userIds.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      await job.updateProgress(100);

      return {
        success: totalSent > 0,
        sentCount: totalSent,
        failedCount: totalFailed,
        removedTokens: allRemovedTokens.length > 0 ? allRemovedTokens : undefined,
        errors: allErrors.length > 0 ? allErrors : undefined,
        message: `Notificación masiva procesada: ${totalSent} enviadas, ${totalFailed} fallidas`,
        processedAt: new Date(),
      };
    } catch (error: any) {
      this.logger.error(`Error en procesamiento de notificación masiva: ${error.message}`);
      throw error;
    }
  }

  private async processCleanup(job: Job<NotificationJobData>, data: CleanupJobData): Promise<CleanupJobResult> {
    try {
      await job.updateProgress(50);

      const result = await this.pushTokenPersistence.deleteOldTokens(data.daysOld);

      await job.updateProgress(100);

      return {
        success: true,
        deletedCount: result.count,
        message: `Limpieza completada: ${result.count} tokens antiguos eliminados`,
        processedAt: new Date(),
      };
    } catch (error: any) {
      this.logger.error(`Error en procesamiento de limpieza: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Método para enviar notificaciones push (adaptado del servicio original)
  private async sendPushNotifications(
    tokens: string[],
    title: string,
    body: string,
    data?: Record<string, any>,
  ): Promise<{
    sentCount: number;
    failedCount: number;
    removedTokens?: string[];
    errors?: Array<{ token: string; error: string }>;
  }> {
    const { messages, removedTokens, invalidTokens } = this.getTokensData(tokens, title, body, data);

    // Eliminar tokens inválidos
    await Promise.all(invalidTokens.map((token: string) => this.removeInvalidToken(token)));

    if (messages.length === 0) {
      return {
        sentCount: 0,
        failedCount: tokens.length,
        removedTokens,
      };
    }

    try {
      const chunks = this.expo.chunkPushNotifications(messages);
      const result = await this.processNotificationChunks(chunks, removedTokens);
      return result;
    } catch (error: any) {
      this.logger.error(`Error crítico enviando notificaciones: ${error.message}`);
      return {
        sentCount: 0,
        failedCount: messages.length,
        errors: [{ token: 'all', error: error.message }],
      };
    }
  }

  private getTokensData(tokens: string[], title: string, body: string, data?: Record<string, any>) {
    const messages: ExpoPushMessage[] = [];
    const removedTokens: string[] = [];
    const invalidTokens: string[] = [];

    for (const token of tokens) {
      if (!Expo.isExpoPushToken(token)) {
        this.logger.warn(`Token inválido detectado: ${token}`);
        removedTokens.push(token);
        invalidTokens.push(token);
      } else {
        const message: ExpoPushMessage = {
          to: token,
          sound: 'default',
          title,
          body,
          data: data || {},
        };
        messages.push(message);
      }
    }

    return { messages, removedTokens, invalidTokens };
  }

  private async processNotificationChunks(chunks: ExpoPushMessage[][], removedTokens: string[]) {
    const errors: Array<{ token: string; error: string }> = [];
    let sentCount = 0;
    let failedCount = 0;

    for (const chunk of chunks) {
      try {
        const ticketChunk = await this.expo.sendPushNotificationsAsync(chunk);
        const chunkResult = await this.processTicketChunk(ticketChunk, chunk, removedTokens);

        sentCount += chunkResult.sentCount;
        failedCount += chunkResult.failedCount;
        errors.push(...chunkResult.errors);
      } catch (chunkError: any) {
        this.logger.error(`Error enviando chunk de notificaciones: ${chunkError.message}`);
        failedCount += chunk.length;
      }
    }

    return { sentCount, failedCount, removedTokens, errors };
  }

  private async processTicketChunk(ticketChunk: ExpoPushTicket[], chunk: ExpoPushMessage[], removedTokens: string[]) {
    const errors: Array<{ token: string; error: string }> = [];
    let sentCount = 0;
    let failedCount = 0;

    for (let i = 0; i < ticketChunk.length; i++) {
      const ticket = ticketChunk[i];
      const originalToken = chunk[i].to as string;

      if (ticket.status === 'error') {
        const errorResult = await this.handleTicketError(ticket, originalToken, removedTokens);
        failedCount++;
        errors.push(errorResult);
      } else {
        sentCount++;
        await this.pushTokenPersistence.updateLastUsed(originalToken);
      }
    }

    return { sentCount, failedCount, errors };
  }

  private async handleTicketError(
    ticket: ExpoPushTicket,
    token: string,
    removedTokens: string[],
  ): Promise<{ token: string; error: string }> {
    const errorMessage =
      ticket.status === 'error' ? ((ticket as any).message ?? 'Error desconocido') : 'Error desconocido';

    this.logger.error(`Error enviando notificación a ${token}: ${errorMessage}`);

    if (this.isTokenInvalidError(errorMessage)) {
      removedTokens.push(token);
      await this.removeInvalidToken(token);
    }

    return { token, error: errorMessage };
  }

  private isTokenInvalidError(errorMessage?: string): boolean {
    if (!errorMessage) return false;

    const invalidTokenErrors = [
      'DeviceNotRegistered',
      'InvalidCredentials',
      'MessageTooBig',
      'InvalidDataKey',
      'MismatchSenderId',
      'InvalidRegistration',
    ];

    return invalidTokenErrors.some((error) => errorMessage.includes(error));
  }

  private async removeInvalidToken(token: string): Promise<void> {
    try {
      await this.pushTokenPersistence.deleteByToken(token);
      this.logger.log(`Token inválido eliminado de la base de datos: ${token}`);
    } catch (error: any) {
      this.logger.error(`Error eliminando token inválido: ${error.message}`);
    }
  }
}
