import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { BullModule } from '@nestjs/bullmq';
import { ScheduleModule } from '@nestjs/schedule';
import { PushNotificationsService } from './push-notifications.service';
import { PushNotificationsController } from './push-notifications.controller';
import { NotificationQueueService } from './services/notification-queue.service';
import { QueueMonitoringService } from './services/queue-monitoring.service';
import { NotificationProcessor } from './processors/notification.processor';
import { PersistenceModule } from '../../persistence/persistence.module';

@Module({
  imports: [
    PersistenceModule,
    ScheduleModule.forRoot(),
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your_jwt_secret',
      signOptions: { expiresIn: '60m' },
    }),
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      },
    }),
    BullModule.registerQueue({
      name: 'push-notifications',
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    }),
  ],
  controllers: [PushNotificationsController],
  providers: [PushNotificationsService, NotificationQueueService, QueueMonitoringService, NotificationProcessor],
  exports: [PushNotificationsService, NotificationQueueService, QueueMonitoringService],
})
export class PushNotificationsModule {}
