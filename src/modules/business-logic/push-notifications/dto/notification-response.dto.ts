import { ApiProperty } from '@nestjs/swagger';

export class NotificationResponseDto {
  @ApiProperty({
    description: 'Indica si la notificación fue enviada exitosamente',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Mensaje descriptivo del resultado',
    example: 'Notificación enviada exitosamente',
  })
  message: string;

  @ApiProperty({
    description: 'Número de notificaciones enviadas exitosamente',
    example: 1,
  })
  sentCount: number;

  @ApiProperty({
    description: 'Número de notificaciones que fallaron',
    example: 0,
  })
  failedCount: number;

  @ApiProperty({
    description: 'Lista de tokens que fueron eliminados por ser inválidos',
    example: ['ExponentPushToken[invalid_token]'],
    required: false,
  })
  removedTokens?: string[];

  @ApiProperty({
    description: 'Detalles de errores si los hay',
    required: false,
  })
  errors?: Array<{
    token: string;
    error: string;
  }>;
}

export class BulkNotificationResponseDto {
  @ApiProperty({
    description: 'Indica si todas las notificaciones fueron procesadas',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Mensaje descriptivo del resultado',
    example: 'Notificaciones masivas procesadas',
  })
  message: string;

  @ApiProperty({
    description: 'Número total de usuarios objetivo',
    example: 10,
  })
  totalUsers: number;

  @ApiProperty({
    description: 'Número de notificaciones enviadas exitosamente',
    example: 8,
  })
  sentCount: number;

  @ApiProperty({
    description: 'Número de notificaciones que fallaron',
    example: 2,
  })
  failedCount: number;

  @ApiProperty({
    description: 'Lista de tokens que fueron eliminados por ser inválidos',
    example: ['ExponentPushToken[invalid_token1]', 'ExponentPushToken[invalid_token2]'],
    required: false,
  })
  removedTokens?: string[];

  @ApiProperty({
    description: 'Detalles de errores por usuario',
    required: false,
  })
  userErrors?: Array<{
    userId: string;
    errors: Array<{
      token: string;
      error: string;
    }>;
  }>;
}

export class TokenStatsResponseDto {
  @ApiProperty({
    description: 'Número total de tokens registrados',
    example: 150,
  })
  totalTokens: number;

  @ApiProperty({
    description: 'Número de usuarios únicos con tokens',
    example: 75,
  })
  uniqueUsers: number;

  @ApiProperty({
    description: 'Estadísticas por tipo de dispositivo',
    example: [
      { device: 'iOS', count: 80 },
      { device: 'Android', count: 65 },
      { device: 'Unknown', count: 5 }
    ],
  })
  deviceStats: Array<{
    device: string;
    count: number;
  }>;
}
