import { ApiProperty } from '@nestjs/swagger';

export class PushTokenResponseDto {
  @ApiProperty({
    description: 'ID único del token push',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Token de notificación push del dispositivo',
    example: 'ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]',
  })
  token: string;

  @ApiProperty({
    description: 'Información del dispositivo',
    example: 'iOS',
    required: false,
  })
  device?: string;

  @ApiProperty({
    description: 'ID del usuario propietario del token',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  userId: string;

  @ApiProperty({
    description: 'Fecha de creación del token',
    example: '2024-07-06T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Fecha de última actualización',
    example: '2024-07-06T15:30:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Fecha de último uso del token',
    example: '2024-07-06T15:30:00.000Z',
  })
  lastUsed: Date;

  @ApiProperty({
    description: 'Información del usuario (opcional)',
    required: false,
  })
  user?: {
    id: string;
    firstName: string;
    paternalLastName: string;
    email: string;
  };
}
