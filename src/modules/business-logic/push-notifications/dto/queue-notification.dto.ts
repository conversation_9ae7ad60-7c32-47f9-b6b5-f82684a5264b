import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsUUID, IsArray, IsObject, IsNumber, Min, Max } from 'class-validator';
import { JobPriority } from '../interfaces/notification-job.interface';

export class QueueNotificationDto {
  @ApiProperty({
    description: 'Título de la notificación',
    example: 'Nueva reserva aprobada',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Cuerpo del mensaje de la notificación',
    example: 'Tu reserva para la piscina ha sido aprobada para mañana a las 10:00 AM',
  })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiProperty({
    description: 'Datos adicionales para la notificación',
    example: { reservationId: '123', type: 'reservation' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @ApiProperty({
    description: 'Token específico para enviar la notificación',
    example: 'ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]',
    required: false,
  })
  @IsOptional()
  @IsString()
  token?: string;

  @ApiProperty({
    description: 'ID del usuario destinatario',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiProperty({
    description: 'Lista de IDs de usuarios destinatarios',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e89b-12d3-a456-************'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  userIds?: string[];

  @ApiProperty({
    description: 'Prioridad del job (1=baja, 5=normal, 10=alta, 20=crítica)',
    example: 5,
    required: false,
    minimum: 1,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  priority?: JobPriority;

  @ApiProperty({
    description: 'Retraso en milisegundos antes de procesar el job',
    example: 0,
    required: false,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  delay?: number;

  @ApiProperty({
    description: 'Número máximo de intentos en caso de fallo',
    example: 3,
    required: false,
    minimum: 1,
    maximum: 10,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  attempts?: number;
}

export class QueueBulkNotificationDto {
  @ApiProperty({
    description: 'Título de la notificación',
    example: 'Anuncio importante',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'Cuerpo del mensaje de la notificación',
    example: 'Se ha publicado un nuevo anuncio en la comunidad',
  })
  @IsString()
  @IsNotEmpty()
  body: string;

  @ApiProperty({
    description: 'Lista de IDs de usuarios destinatarios',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e89b-12d3-a456-************'],
  })
  @IsArray()
  @IsUUID(4, { each: true })
  userIds: string[];

  @ApiProperty({
    description: 'Datos adicionales para la notificación',
    example: { announcementId: '123', type: 'announcement' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @ApiProperty({
    description: 'Tamaño del lote para procesamiento',
    example: 100,
    required: false,
    minimum: 10,
    maximum: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(10)
  @Max(1000)
  batchSize?: number;

  @ApiProperty({
    description: 'Prioridad del job (1=baja, 5=normal, 10=alta, 20=crítica)',
    example: 5,
    required: false,
    minimum: 1,
    maximum: 20,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  priority?: JobPriority;

  @ApiProperty({
    description: 'Retraso en milisegundos antes de procesar el job',
    example: 0,
    required: false,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  delay?: number;
}

export class QueueJobResponseDto {
  @ApiProperty({
    description: 'ID del job en la cola',
    example: 'job_123456789',
  })
  jobId: string;

  @ApiProperty({
    description: 'Nombre de la cola',
    example: 'push-notifications',
  })
  queueName: string;

  @ApiProperty({
    description: 'Estado del job',
    example: 'waiting',
  })
  status: string;

  @ApiProperty({
    description: 'Prioridad del job',
    example: 5,
  })
  priority: number;

  @ApiProperty({
    description: 'Timestamp de creación',
    example: '2024-07-06T10:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Retraso programado en milisegundos',
    example: 0,
    required: false,
  })
  delay?: number;

  @ApiProperty({
    description: 'Mensaje de confirmación',
    example: 'Notificación agregada a la cola para procesamiento',
  })
  message: string;
}
