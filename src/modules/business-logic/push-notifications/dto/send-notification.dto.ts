import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsArray, IsUUID, MaxLength, IsObject } from 'class-validator';

export class SendNotificationDto {
  @ApiProperty({
    description: 'Título de la notificación',
    example: 'Nueva reserva aprobada',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  title: string;

  @ApiProperty({
    description: 'Cuerpo del mensaje de la notificación',
    example: 'Tu reserva para la piscina ha sido aprobada para mañana a las 10:00 AM',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  body: string;

  @ApiProperty({
    description: 'Datos adicionales para la notificación',
    example: { reservationId: '123', type: 'reservation' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @ApiProperty({
    description: 'ID del usuario destinatario (opcional si se especifican userIds)',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiProperty({
    description: 'Lista de IDs de usuarios destinatarios (opcional si se especifica userId)',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e12b-34c5-d678-************'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID('4', { each: true })
  userIds?: string[];

  @ApiProperty({
    description: 'Token específico para enviar la notificación (opcional)',
    example: 'ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]',
    required: false,
  })
  @IsOptional()
  @IsString()
  token?: string;
}

export class SendBulkNotificationDto {
  @ApiProperty({
    description: 'Título de la notificación',
    example: 'Mantenimiento programado',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  title: string;

  @ApiProperty({
    description: 'Cuerpo del mensaje de la notificación',
    example: 'Se realizará mantenimiento en las áreas comunes el próximo sábado',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  body: string;

  @ApiProperty({
    description: 'Datos adicionales para la notificación',
    example: { type: 'maintenance', date: '2024-07-13' },
    required: false,
  })
  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @ApiProperty({
    description: 'Lista de IDs de usuarios destinatarios',
    example: ['123e4567-e89b-12d3-a456-************', '456e7890-e12b-34c5-d678-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  userIds: string[];
}
