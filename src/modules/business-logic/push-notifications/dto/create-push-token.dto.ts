import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, MaxLength } from 'class-validator';

export class CreatePushTokenDto {
  @ApiProperty({
    description: 'Token de notificación push del dispositivo',
    example: 'ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(500)
  token: string;

  @ApiProperty({
    description: 'Información del dispositivo (iOS, Android, etc.)',
    example: 'iOS',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  device: string;
}
