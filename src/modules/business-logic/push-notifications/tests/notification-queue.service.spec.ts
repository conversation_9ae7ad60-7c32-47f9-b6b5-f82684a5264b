import { Test, TestingModule } from '@nestjs/testing';
import { getQueueToken } from '@nestjs/bullmq';
import { Logger } from 'nestjs-pino';
import { NotificationQueueService } from '../services/notification-queue.service';
import { JobPriority } from '../interfaces/notification-job.interface';

describe('NotificationQueueService', () => {
  let service: NotificationQueueService;
  let mockQueue: any;
  let mockLogger: any;

  beforeEach(async () => {
    // Mock de la cola BullMQ
    mockQueue = {
      add: jest.fn(),
      getJobs: jest.fn(),
      getJobCounts: jest.fn(),
      getWaiting: jest.fn(),
      getActive: jest.fn(),
      getCompleted: jest.fn(),
      getFailed: jest.fn(),
      getDelayed: jest.fn(),
      isPaused: jest.fn(),
      pause: jest.fn(),
      resume: jest.fn(),
      clean: jest.fn(),
      getJob: jest.fn(),
    };

    // Mock del logger
    mockLogger = {
      log: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationQueueService,
        {
          provide: getQueueToken('push-notifications'),
          useValue: mockQueue,
        },
        {
          provide: Logger,
          useValue: mockLogger,
        },
      ],
    }).compile();

    service = module.get<NotificationQueueService>(NotificationQueueService);

    // Inyectar manualmente la cola mock para evitar la inicialización real
    (service as any).notificationQueue = mockQueue;
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('addSingleNotification', () => {
    it('should add a single notification job to the queue', async () => {
      const notificationData = {
        token: 'ExponentPushToken[test]',
        title: 'Test Title',
        body: 'Test Body',
        data: { key: 'value' },
        priority: JobPriority.NORMAL,
      };

      const mockJob = {
        id: 'job-123',
        name: 'single-notification',
        data: expect.any(Object),
        opts: {
          priority: JobPriority.NORMAL,
          delay: 0,
        },
        timestamp: Date.now(),
      };

      mockQueue.add.mockResolvedValue(mockJob);

      const result = await service.addSingleNotification(notificationData);

      expect(mockQueue.add).toHaveBeenCalledWith(
        'single-notification',
        {
          type: 'single',
          title: notificationData.title,
          body: notificationData.body,
          data: notificationData.data,
          token: notificationData.token,
          userId: undefined,
          userIds: undefined,
          priority: notificationData.priority,
          delay: 0,
          attempts: 3,
        },
        {
          priority: expect.any(Number),
          delay: 0,
          attempts: 3,
        },
      );

      expect(result).toEqual({
        jobId: 'job-123',
        queueName: 'push-notifications',
        status: 'queued',
        message: 'Notificación individual agregada a la cola',
        priority: JobPriority.NORMAL,
        createdAt: expect.any(Date),
        delay: 0,
      });
    });

    it('should handle errors when adding job fails', async () => {
      const notificationData = {
        token: 'ExponentPushToken[test]',
        title: 'Test Title',
        body: 'Test Body',
        priority: JobPriority.NORMAL,
      };

      mockQueue.add.mockRejectedValue(new Error('Queue error'));

      await expect(service.addSingleNotification(notificationData)).rejects.toThrow('Queue error');
      expect(mockLogger.error).toHaveBeenCalled();
    });
  });

  describe('addBulkNotification', () => {
    it('should add a bulk notification job to the queue', async () => {
      const bulkData = {
        title: 'Bulk Notification Title',
        body: 'Bulk Notification Body',
        userIds: ['123e4567-e89b-12d3-a456-426614174000', '456e7890-e89b-12d3-a456-426614174001'],
        data: { key: 'value' },
        batchSize: 10,
        priority: JobPriority.HIGH,
      };

      const mockJob = {
        id: 'bulk-job-456',
        name: 'bulk-notification',
        data: expect.any(Object),
        opts: {
          priority: JobPriority.HIGH,
          delay: 0,
        },
        timestamp: Date.now(),
      };

      mockQueue.add.mockResolvedValue(mockJob);

      const result = await service.addBulkNotification(bulkData);

      expect(mockQueue.add).toHaveBeenCalledWith(
        'bulk-notification',
        {
          type: 'bulk',
          title: bulkData.title,
          body: bulkData.body,
          userIds: bulkData.userIds,
          data: bulkData.data,
          batchSize: bulkData.batchSize,
          priority: bulkData.priority,
          delay: 0,
        },
        {
          priority: expect.any(Number),
          delay: 0,
          attempts: 3,
        },
      );

      expect(result.jobId).toBe('bulk-job-456');
      expect(result.status).toBe('queued');
    });
  });

  describe('addCleanupJob', () => {
    it('should add a cleanup job to the queue', async () => {
      const daysOld = 30;
      const mockJob = {
        id: 'cleanup-job-789',
        name: 'cleanup-tokens',
        data: expect.any(Object),
        opts: {
          priority: JobPriority.LOW,
          delay: 0,
        },
        timestamp: Date.now(),
      };

      mockQueue.add.mockResolvedValue(mockJob);

      const result = await service.addCleanupJob(daysOld);

      expect(mockQueue.add).toHaveBeenCalledWith(
        'cleanup-tokens',
        {
          type: 'cleanup',
          daysOld: daysOld,
        },
        {
          priority: JobPriority.LOW,
          delay: 0,
          attempts: 2,
        },
      );

      expect(result.jobId).toBe('cleanup-job-789');
    });
  });

  describe('getQueueStats', () => {
    it('should return queue statistics', async () => {
      // Los métodos devuelven arrays, no números
      mockQueue.getWaiting.mockResolvedValue(new Array(5)); // 5 jobs waiting
      mockQueue.getActive.mockResolvedValue(new Array(2)); // 2 jobs active
      mockQueue.getCompleted.mockResolvedValue(new Array(100)); // 100 jobs completed
      mockQueue.getFailed.mockResolvedValue(new Array(3)); // 3 jobs failed
      mockQueue.getDelayed.mockResolvedValue(new Array(1)); // 1 job delayed
      mockQueue.isPaused.mockResolvedValue(false);

      const result = await service.getQueueStats();

      expect(result).toEqual({
        queueName: 'push-notifications',
        counts: {
          waiting: 5,
          active: 2,
          completed: 100,
          failed: 3,
          delayed: 1,
        },
        health: {
          isReady: true,
          isPaused: false,
        },
      });
    });

    it('should handle paused queue', async () => {
      mockQueue.getWaiting.mockResolvedValue(new Array(10)); // 10 jobs waiting
      mockQueue.getActive.mockResolvedValue(new Array(0)); // 0 jobs active
      mockQueue.getCompleted.mockResolvedValue(new Array(50)); // 50 jobs completed
      mockQueue.getFailed.mockResolvedValue(new Array(5)); // 5 jobs failed
      mockQueue.getDelayed.mockResolvedValue(new Array(0)); // 0 jobs delayed
      mockQueue.isPaused.mockResolvedValue(true);

      const result = await service.getQueueStats();

      expect(result.health.isPaused).toBe(true);
      expect(result.health.isReady).toBe(false);
    });
  });

  describe('pauseQueue', () => {
    it('should pause the queue', async () => {
      mockQueue.pause.mockResolvedValue(undefined);

      await service.pauseQueue();

      expect(mockQueue.pause).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith('Cola de notificaciones pausada');
    });
  });

  describe('resumeQueue', () => {
    it('should resume the queue', async () => {
      mockQueue.resume.mockResolvedValue(undefined);

      await service.resumeQueue();

      expect(mockQueue.resume).toHaveBeenCalled();
      expect(mockLogger.log).toHaveBeenCalledWith('Cola de notificaciones reanudada');
    });
  });

  describe('getJob', () => {
    it('should return job information', async () => {
      const jobId = 'test-job-123';
      const mockJob = {
        id: jobId,
        name: 'single-notification',
        data: { title: 'Test' },
        progress: 50,
      };

      mockQueue.getJob.mockResolvedValue(mockJob);

      const result = await service.getJob(jobId);

      expect(mockQueue.getJob).toHaveBeenCalledWith(jobId);
      expect(result).toBe(mockJob);
    });

    it('should return null for non-existent job', async () => {
      const jobId = 'non-existent-job';
      mockQueue.getJob.mockResolvedValue(null);

      const result = await service.getJob(jobId);

      expect(result).toBeNull();
    });
  });

  describe('cleanQueue', () => {
    it('should clean completed and failed jobs', async () => {
      mockQueue.clean.mockResolvedValue([10, 5]); // 10 completed, 5 failed cleaned

      await service.cleanQueue();

      expect(mockQueue.clean).toHaveBeenCalledTimes(2);
      expect(mockQueue.clean).toHaveBeenCalledWith(24 * 60 * 60 * 1000, 100, 'completed');
      expect(mockQueue.clean).toHaveBeenCalledWith(7 * 24 * 60 * 60 * 1000, 50, 'failed');
      expect(mockLogger.log).toHaveBeenCalledWith('Cola de notificaciones limpiada');
    });
  });
});
