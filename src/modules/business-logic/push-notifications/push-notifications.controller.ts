import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUI<PERSON>ipe,
  HttpCode,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/jwt-auth.guard';
import { PushNotificationsService } from './push-notifications.service';
import { QueueMonitoringService } from './services/queue-monitoring.service';
import { QueueJobResponseDto } from './dto/queue-notification.dto';
import { PushTokenResponseDto } from './dto/push-token-response.dto';
import { TokenStatsResponseDto } from './dto/notification-response.dto';

@ApiTags('Push Notifications')
@Controller('push-notifications')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PushNotificationsController {
  constructor(
    private readonly pushNotificationsService: PushNotificationsService,
    private readonly queueMonitoringService: QueueMonitoringService,
  ) {}

  // 🔹 Eliminar token push
  @Delete('tokens/:tokenId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Eliminar un token push' })
  @ApiResponse({ status: 204, description: 'Token eliminado exitosamente' })
  @ApiResponse({ status: 404, description: 'Token no encontrado' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async removeToken(@Param('tokenId', ParseUUIDPipe) tokenId: string): Promise<void> {
    return this.pushNotificationsService.removeToken(tokenId);
  }

  // 🔹 Eliminar token por valor
  @Delete('tokens/by-value/:token')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Eliminar un token push por su valor' })
  @ApiResponse({ status: 204, description: 'Token eliminado exitosamente' })
  @ApiResponse({ status: 404, description: 'Token no encontrado' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async removeTokenByValue(@Param('token') token: string): Promise<void> {
    return this.pushNotificationsService.removeTokenByValue(token);
  }

  // 🔹 Limpiar tokens antiguos (usando cola)
  @Delete('cleanup')
  @ApiOperation({ summary: 'Agregar job de limpieza de tokens antiguos a la cola' })
  @ApiQuery({
    name: 'days',
    required: false,
    description: 'Días de antigüedad para considerar tokens como antiguos (default: 30)',
  })
  @ApiResponse({ status: 200, description: 'Job de limpieza agregado a la cola', type: QueueJobResponseDto })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async cleanupOldTokens(@Query('days') days?: string): Promise<QueueJobResponseDto> {
    const daysOld = days ? parseInt(days, 10) : 30;
    return this.pushNotificationsService.cleanupOldTokens(daysOld);
  }

  // 🔹 Obtener todos los tokens (solo administradores)
  @Get('tokens/all')
  @ApiOperation({ summary: 'Obtener todos los tokens push (solo administradores)' })
  @ApiResponse({ status: 200, description: 'Lista de todos los tokens', type: [PushTokenResponseDto] })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async getAllTokens(): Promise<PushTokenResponseDto[]> {
    return this.pushNotificationsService.getAllTokens();
  }

  // 🔹 Obtener tokens de un usuario específico (solo administradores)
  @Get('tokens/user/:userId')
  @ApiOperation({ summary: 'Obtener tokens de un usuario específico (solo administradores)' })
  @ApiResponse({ status: 200, description: 'Lista de tokens del usuario', type: [PushTokenResponseDto] })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async getUserTokens(@Param('userId', ParseUUIDPipe) userId: string): Promise<PushTokenResponseDto[]> {
    return this.pushNotificationsService.getUserTokens(userId);
  }

  // 🔹 Obtener estadísticas de tokens
  @Get('tokens/stats')
  @ApiOperation({ summary: 'Obtener estadísticas de tokens push' })
  @ApiResponse({ status: 200, description: 'Estadísticas de tokens', type: TokenStatsResponseDto })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async getTokenStats(): Promise<TokenStatsResponseDto> {
    return this.pushNotificationsService.getTokenStats();
  }

  // 🔹 Obtener estadísticas de la cola
  @Get('queue/stats')
  @ApiOperation({ summary: 'Obtener estadísticas de la cola de notificaciones' })
  @ApiResponse({ status: 200, description: 'Estadísticas de la cola' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async getQueueStats() {
    return this.pushNotificationsService.getQueueStats();
  }

  // 🔹 Obtener métricas detalladas de la cola
  @Get('queue/metrics')
  @ApiOperation({ summary: 'Obtener métricas detalladas de la cola' })
  @ApiResponse({ status: 200, description: 'Métricas detalladas de la cola' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async getQueueMetrics() {
    return this.queueMonitoringService.getDetailedMetrics();
  }

  // 🔹 Obtener información de un job específico
  @Get('queue/job/:jobId')
  @ApiOperation({ summary: 'Obtener información de un job específico' })
  @ApiResponse({ status: 200, description: 'Información del job' })
  @ApiResponse({ status: 404, description: 'Job no encontrado' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async getJobInfo(@Param('jobId') jobId: string) {
    const jobInfo = await this.queueMonitoringService.getJobInfo(jobId);
    if (!jobInfo) {
      throw new NotFoundException(`Job con ID ${jobId} no encontrado`);
    }
    return jobInfo;
  }

  // 🔹 Pausar la cola de notificaciones
  @Post('queue/pause')
  @ApiOperation({ summary: 'Pausar el procesamiento de la cola' })
  @ApiResponse({ status: 200, description: 'Cola pausada exitosamente' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async pauseQueue() {
    await this.queueMonitoringService.pauseProcessing();
    return { message: 'Cola pausada exitosamente' };
  }

  // 🔹 Reanudar la cola de notificaciones
  @Post('queue/resume')
  @ApiOperation({ summary: 'Reanudar el procesamiento de la cola' })
  @ApiResponse({ status: 200, description: 'Cola reanudada exitosamente' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async resumeQueue() {
    await this.queueMonitoringService.resumeProcessing();
    return { message: 'Cola reanudada exitosamente' };
  }

  // 🔹 Limpiar jobs de la cola
  @Delete('queue/clean')
  @ApiOperation({ summary: 'Limpiar jobs completados y fallidos de la cola' })
  @ApiResponse({ status: 200, description: 'Cola limpiada exitosamente' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async cleanQueue() {
    await this.queueMonitoringService.cleanupOldJobs();
    return { message: 'Cola limpiada exitosamente' };
  }

  // 🔹 Reintentar jobs fallidos
  @Post('queue/retry')
  @ApiOperation({ summary: 'Reintentar jobs fallidos' })
  @ApiResponse({ status: 200, description: 'Jobs reintentados exitosamente' })
  @ApiResponse({ status: 401, description: 'No autorizado' })
  async retryFailedJobs(@Body() body?: { jobIds?: string[] }) {
    const result = await this.queueMonitoringService.retryFailedJobs(body?.jobIds);
    return {
      message: `${result.retriedCount} jobs reintentados exitosamente`,
      retriedCount: result.retriedCount,
    };
  }
}
