# Push Notifications Module

Este módulo maneja las notificaciones push para el sistema HOA Manager usando Expo Push Notifications.

## Características

- ✅ Registro y gestión de tokens push
- ✅ Envío de notificaciones individuales y masivas
- ✅ Validación automática de tokens con Expo
- ✅ Eliminación automática de tokens inválidos
- ✅ Autenticación JWT con Bearer tokens
- ✅ Documentación Swagger completa
- ✅ Limpieza automática de tokens antiguos
- ✅ Estadísticas de tokens por dispositivo

## Configuración

### Variables de Entorno

Agregar al archivo `.env`:

```bash
# Expo Push Notifications
EXPO_ACCESS_TOKEN="your-expo-access-token"
```

### Obtener Access Token de Expo

1. Instalar Expo CLI: `npm install -g @expo/cli`
2. Iniciar sesión: `expo login`
3. Generar token: `expo whoami --json` o usar el dashboard de Expo

## Endpoints API

### Autenticación

Todos los endpoints requieren autenticación JWT con Bearer token:

```
Authorization: Bearer <your-jwt-token>
```

### Gestión de Tokens

#### Registrar Token Push
```http
POST /push-notifications/tokens
Content-Type: application/json

{
  "token": "ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]",
  "device": "iOS" // opcional
}
```

#### Obtener Mis Tokens
```http
GET /push-notifications/tokens/my-tokens
```

#### Actualizar Token
```http
PATCH /push-notifications/tokens/{tokenId}
Content-Type: application/json

{
  "device": "Android"
}
```

#### Eliminar Token
```http
DELETE /push-notifications/tokens/{tokenId}
```

#### Eliminar Token por Valor
```http
DELETE /push-notifications/tokens/by-value/{token}
```

### Envío de Notificaciones

#### Notificación Individual
```http
POST /push-notifications/send
Content-Type: application/json

{
  "title": "Título de la notificación",
  "body": "Mensaje de la notificación",
  "userId": "uuid-del-usuario", // opcional
  "token": "ExponentPushToken[xxx]", // opcional
  "userIds": ["uuid1", "uuid2"], // opcional
  "data": { // opcional
    "type": "announcement",
    "id": "123"
  }
}
```

#### Notificaciones Masivas
```http
POST /push-notifications/send-bulk
Content-Type: application/json

{
  "title": "Notificación masiva",
  "body": "Mensaje para todos",
  "userIds": ["uuid1", "uuid2", "uuid3"],
  "data": {
    "type": "emergency",
    "priority": "high"
  }
}
```

### Administración (Solo Administradores)

#### Estadísticas de Tokens
```http
GET /push-notifications/stats
```

#### Limpiar Tokens Antiguos
```http
DELETE /push-notifications/cleanup?days=30
```

#### Obtener Todos los Tokens
```http
GET /push-notifications/tokens/all
```

#### Obtener Tokens de Usuario
```http
GET /push-notifications/tokens/user/{userId}
```

## Manejo de Errores

El módulo maneja automáticamente los siguientes errores de Expo:

- `DeviceNotRegistered`: Token no registrado en el dispositivo
- `InvalidCredentials`: Credenciales inválidas
- `MessageTooBig`: Mensaje demasiado grande
- `InvalidDataKey`: Clave de datos inválida
- `MismatchSenderId`: ID de remitente incorrecto
- `InvalidRegistration`: Registro inválido

Cuando se detectan estos errores, el token se elimina automáticamente de la base de datos.

## Estructura del Módulo

```
src/modules/business-logic/push-notifications/
├── dto/
│   ├── create-push-token.dto.ts
│   ├── update-push-token.dto.ts
│   ├── push-token-response.dto.ts
│   ├── send-notification.dto.ts
│   └── notification-response.dto.ts
├── push-notifications.controller.ts
├── push-notifications.service.ts
├── push-notifications.module.ts
└── README.md
```

## Integración con Aplicación Móvil

### Registro de Token

```javascript
import { Notifications } from 'expo-notifications';

// Obtener token push
const token = await Notifications.getExpoPushTokenAsync();

// Registrar en el backend
await fetch('/api/push-notifications/tokens', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${userToken}`
  },
  body: JSON.stringify({
    token: token.data,
    device: Platform.OS
  })
});
```

### Configurar Notificaciones

```javascript
import { Notifications } from 'expo-notifications';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});
```

## Pruebas

Para probar el módulo:

1. Registrar un token válido de Expo
2. Enviar una notificación de prueba
3. Verificar que la notificación llegue al dispositivo
4. Probar con tokens inválidos para verificar la limpieza automática

## Notas Importantes

- Los tokens de Expo tienen el formato: `ExponentPushToken[xxxxxxxxxxxxxxxxxxxxxx]`
- Los tokens se validan automáticamente antes de ser almacenados
- Los tokens inválidos se eliminan automáticamente de la base de datos
- Se recomienda ejecutar la limpieza de tokens antiguos periódicamente
- El módulo es compatible con iOS y Android a través de Expo
