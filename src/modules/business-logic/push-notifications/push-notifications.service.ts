import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { Expo } from 'expo-server-sdk';
import { PushTokenPersistenceService } from '../../persistence/push-token-persistence.service';
import { NotificationQueueService } from './services/notification-queue.service';
import { CreatePushTokenDto } from './dto/create-push-token.dto';
import { UpdatePushTokenDto } from './dto/update-push-token.dto';
import { SendNotificationDto, SendBulkNotificationDto } from './dto/send-notification.dto';
import { QueueNotificationDto, QueueBulkNotificationDto, QueueJobResponseDto } from './dto/queue-notification.dto';
import { TokenStatsResponseDto } from './dto/notification-response.dto';
import { PushTokenResponseDto } from './dto/push-token-response.dto';
import { JobPriority } from './interfaces/notification-job.interface';

@Injectable()
export class PushNotificationsService {
  constructor(
    private readonly pushTokenPersistence: PushTokenPersistenceService,
    private readonly notificationQueue: NotificationQueueService,
    private readonly logger: Logger,
  ) {}

  // 🔹 Registrar un nuevo token push
  async registerToken(createTokenDto: CreatePushTokenDto, userId: string): Promise<PushTokenResponseDto> {
    try {
      // Agregar job de limpieza si es el primer token registrado
      const tokenCount = await this.pushTokenPersistence.getFirstRecord();
      if (tokenCount === null) {
        await this.notificationQueue.addCleanupJob();
      }
      // Validar que el token sea válido para Expo
      if (!Expo.isExpoPushToken(createTokenDto.token)) {
        throw new BadRequestException('Token de push inválido para Expo');
      }

      // Verificar si el token ya existe
      const existingToken = await this.pushTokenPersistence.findByToken(createTokenDto.token);
      if (existingToken) {
        // Si existe, actualizar el último uso y retornar
        await this.pushTokenPersistence.updateLastUsed(createTokenDto.token);
        return existingToken;
      }

      // Crear nuevo token
      const newToken = await this.pushTokenPersistence.create(createTokenDto, userId);
      this.logger.log(`Token push registrado para usuario ${userId}`);

      //Envía notificación de alta de notificaciones exitosa
      await this.sendNotification({
        title: 'Notificaciones habilitadas',
        body: 'Has habilitado las notificaciones para este dispositivo',
        userId: userId,
      });

      return newToken;
    } catch (error: any) {
      this.logger.error(`Error registrando token push: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Obtener tokens de un usuario
  async getUserTokens(userId: string): Promise<PushTokenResponseDto[]> {
    return this.pushTokenPersistence.findByUserId(userId);
  }

  // 🔹 Actualizar información de un token
  async updateToken(tokenId: string, updateTokenDto: UpdatePushTokenDto): Promise<PushTokenResponseDto> {
    const existingToken = await this.pushTokenPersistence.findById(tokenId);
    if (!existingToken) {
      throw new NotFoundException('Token push no encontrado');
    }

    return this.pushTokenPersistence.update(tokenId, updateTokenDto);
  }

  // 🔹 Eliminar un token
  async removeToken(tokenId: string): Promise<void> {
    const existingToken = await this.pushTokenPersistence.findById(tokenId);
    if (!existingToken) {
      throw new NotFoundException('Token push no encontrado');
    }

    await this.pushTokenPersistence.delete(tokenId);
    this.logger.log(`Token push eliminado: ${tokenId}`);
  }

  // 🔹 Eliminar token por valor
  async removeTokenByValue(token: string): Promise<void> {
    const existingToken = await this.pushTokenPersistence.findByToken(token);
    if (!existingToken) {
      throw new NotFoundException('Token push no encontrado');
    }

    await this.pushTokenPersistence.deleteByToken(token);
    this.logger.log(`Token push eliminado por valor: ${token}`);
  }

  // 🔹 Enviar notificación a un usuario específico (usando cola)
  async sendNotification(sendNotificationDto: SendNotificationDto): Promise<QueueJobResponseDto> {
    try {
      // Validar que se especifique al menos un destinatario
      if (
        !sendNotificationDto.token &&
        !sendNotificationDto.userId &&
        (!sendNotificationDto.userIds || sendNotificationDto.userIds.length === 0)
      ) {
        throw new BadRequestException('Debe especificar userId, userIds o token');
      }

      // Crear DTO para la cola
      const queueData: QueueNotificationDto = {
        title: sendNotificationDto.title,
        body: sendNotificationDto.body,
        data: sendNotificationDto.data,
        token: sendNotificationDto.token,
        userId: sendNotificationDto.userId,
        userIds: sendNotificationDto.userIds,
        priority: JobPriority.NORMAL,
        delay: 0,
        attempts: 3,
      };

      // Agregar a la cola
      return await this.notificationQueue.addSingleNotification(queueData);
    } catch (error: any) {
      this.logger.error(`Error agregando notificación a la cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Enviar notificaciones masivas (usando cola)
  async sendBulkNotification(sendBulkDto: SendBulkNotificationDto): Promise<QueueJobResponseDto> {
    try {
      if (!sendBulkDto.userIds || sendBulkDto.userIds.length === 0) {
        throw new BadRequestException('Debe especificar al menos un usuario destinatario');
      }

      // Crear DTO para la cola
      const queueData: QueueBulkNotificationDto = {
        title: sendBulkDto.title,
        body: sendBulkDto.body,
        data: sendBulkDto.data,
        userIds: sendBulkDto.userIds,
        batchSize: 100, // Procesar en lotes de 100
        priority: JobPriority.NORMAL,
        delay: 0,
      };

      // Agregar a la cola
      return await this.notificationQueue.addBulkNotification(queueData);
    } catch (error: any) {
      this.logger.error(`Error agregando notificación masiva a la cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Limpiar tokens antiguos (usando cola)
  async cleanupOldTokens(daysOld: number = 30): Promise<QueueJobResponseDto> {
    try {
      return await this.notificationQueue.addCleanupJob(daysOld);
    } catch (error: any) {
      this.logger.error(`Error agregando job de limpieza a la cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Obtener estadísticas de tokens
  async getTokenStats(): Promise<TokenStatsResponseDto> {
    return this.pushTokenPersistence.getTokenStats();
  }

  // 🔹 Obtener todos los tokens (para administradores)
  async getAllTokens(): Promise<PushTokenResponseDto[]> {
    return this.pushTokenPersistence.findAll();
  }

  // 🔹 Obtener estadísticas de la cola de notificaciones
  async getQueueStats() {
    try {
      return await this.notificationQueue.getQueueStats();
    } catch (error: any) {
      this.logger.error(`Error obteniendo estadísticas de la cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Obtener job por ID
  async getJob(jobId: string) {
    try {
      return await this.notificationQueue.getJob(jobId);
    } catch (error: any) {
      this.logger.error(`Error obteniendo job ${jobId}: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Pausar la cola de notificaciones
  async pauseQueue(): Promise<void> {
    try {
      await this.notificationQueue.pauseQueue();
    } catch (error: any) {
      this.logger.error(`Error pausando la cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Reanudar la cola de notificaciones
  async resumeQueue(): Promise<void> {
    try {
      await this.notificationQueue.resumeQueue();
    } catch (error: any) {
      this.logger.error(`Error reanudando la cola: ${error.message}`);
      throw error;
    }
  }

  // 🔹 Limpiar jobs de la cola
  async cleanQueue(): Promise<void> {
    try {
      await this.notificationQueue.cleanQueue();
    } catch (error: any) {
      this.logger.error(`Error limpiando la cola: ${error.message}`);
      throw error;
    }
  }
}
