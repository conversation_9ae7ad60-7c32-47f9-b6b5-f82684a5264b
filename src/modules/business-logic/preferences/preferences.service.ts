import { Injectable } from '@nestjs/common';
import { CreatePreferencesDto } from './dto/create-preferences.dto';
import { UpdatePreferencesDto } from './dto/update-preferences.dto';
import { PreferencesPersistenceService } from '../../persistence/preferences-persistence.service';
import { CreateMobilePreferencesDto } from '../../mobile/dto/create-mobile-preferences.dto';
import { UpdateMobilePreferencesDto } from '../../mobile/dto/update-mobile-preferences.dto';

@Injectable()
export class PreferencesService {
  constructor(private readonly preferencesPersistence: PreferencesPersistenceService) {}

  async create(data: CreatePreferencesDto) {
    const { userId, ...preferencesData } = data;
    return this.preferencesPersistence.create(userId, preferencesData);
  }

  async findByUserId(userId: string) {
    return this.preferencesPersistence.findByUserId(userId);
  }

  async findById(preferencesId: string) {
    return this.preferencesPersistence.findById(preferencesId);
  }

  async update(userId: string, data: UpdatePreferencesDto) {
    return this.preferencesPersistence.update(userId, data);
  }

  async upsert(userId: string, data: CreateMobilePreferencesDto | UpdateMobilePreferencesDto) {
    return this.preferencesPersistence.upsert(userId, data);
  }

  async delete(userId: string) {
    return this.preferencesPersistence.delete(userId);
  }
}
