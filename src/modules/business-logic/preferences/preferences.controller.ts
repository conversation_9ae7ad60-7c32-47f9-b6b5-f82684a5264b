import { Controller, Post, Get, Patch, Delete, Body, Param } from '@nestjs/common';
import { PreferencesService } from './preferences.service';
import { CreatePreferencesDto } from './dto/create-preferences.dto';
import { UpdatePreferencesDto } from './dto/update-preferences.dto';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('Preferences')
@Controller('preferences')
export class PreferencesController {
  constructor(private readonly preferencesService: PreferencesService) {}

  @ApiOperation({ summary: 'Crear nuevas preferencias de usuario' })
  @ApiResponse({ status: 201, description: 'Preferencias creadas exitosamente' })
  @Post()
  async create(@Body() createDto: CreatePreferencesDto) {
    return this.preferencesService.create(createDto);
  }

  @ApiOperation({ summary: 'Obtener preferencias por ID de usuario' })
  @ApiResponse({ status: 200, description: 'Preferencias del usuario' })
  @Get('/user/:userId')
  async findByUserId(@Param('userId') userId: string) {
    return this.preferencesService.findByUserId(userId);
  }

  @ApiOperation({ summary: 'Obtener preferencias por ID' })
  @ApiResponse({ status: 200, description: 'Detalles de las preferencias' })
  @Get('/:preferencesId')
  async findById(@Param('preferencesId') preferencesId: string) {
    return this.preferencesService.findById(preferencesId);
  }

  @ApiOperation({ summary: 'Actualizar preferencias de usuario' })
  @ApiResponse({ status: 200, description: 'Preferencias actualizadas exitosamente' })
  @Patch('/user/:userId')
  async update(@Param('userId') userId: string, @Body() updateDto: UpdatePreferencesDto) {
    return this.preferencesService.update(userId, updateDto);
  }

  @ApiOperation({ summary: 'Eliminar preferencias de usuario' })
  @ApiResponse({ status: 200, description: 'Preferencias eliminadas exitosamente' })
  @Delete('/user/:userId')
  async delete(@Param('userId') userId: string) {
    return this.preferencesService.delete(userId);
  }
}
