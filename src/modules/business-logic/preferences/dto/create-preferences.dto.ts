import { IsBoolean, IsOptional, IsString, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreatePreferencesDto {
  @ApiProperty({
    description: 'ID del usuario',
    example: 'user-uuid',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Modo oscuro activado',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  darkMode?: boolean;

  @ApiProperty({
    description: 'Idioma preferido del usuario',
    example: 'es',
    required: false,
  })
  @IsOptional()
  @IsString()
  language?: string;

  @ApiProperty({
    description: 'ID de la propiedad preferida del usuario',
    example: 'property-uuid',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  preferredPropertyId?: string;

  @ApiProperty({
    description: 'Recibir notificaciones por email',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  notificateByEmail?: boolean;
}
