import { Injectable } from '@nestjs/common';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { PropertyPersistenceService } from '../../persistence/property-persistence.service';
import { Property } from '@prisma/client';
import { PropertyResponseDto } from './dto/response-property.dto';
import { ErrorHandlerService } from 'src/modules/error-handler/error-handler.service';
import { GetMobilePropertiesDto } from 'src/modules/mobile/dto/get-mobile-properties.dto';

@Injectable()
export class PropertyService {
  constructor(
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly propertyPersistenceService: PropertyPersistenceService,
  ) {}

  async create(createPropertyDto: CreatePropertyDto) {
    try {
      return await this.propertyPersistenceService.create(createPropertyDto);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't create Property", error.statusCode);
    }
  }

  async findAll() {
    try {
      return await this.propertyPersistenceService.findAll();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all Properties", error.statusCode);
    }
  }

  async findAllForSelect() {
    try {
      return await this.propertyPersistenceService.findAllForSelect();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all Properties", error.statusCode);
    }
  }

  async findOne(id: Property['id']): Promise<PropertyResponseDto> {
    try {
      return await this.propertyPersistenceService.findById(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property", error.statusCode);
    }
  }

  async update(id: Property['id'], updatePropertyDto: UpdatePropertyDto): Promise<PropertyResponseDto> {
    try {
      return await this.propertyPersistenceService.update(id, updatePropertyDto);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't update Property", error.statusCode);
    }
  }

  async delete(id: Property['id']): Promise<PropertyResponseDto> {
    try {
      return await this.propertyPersistenceService.delete(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't delete completly Property", error.statusCode);
    }
  }

  async findForMobileByUserId(userId: string): Promise<GetMobilePropertiesDto[]> {
    try {
      return await this.propertyPersistenceService.findForMobileByUserId(userId);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Properties for mobile", error.statusCode);
    }
  }

  async getResidentsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getResidentsByPropertyId(propertyId)).residents;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property residents", error.statusCode);
    }
  }

  async getVehiclesByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getVehiclesByPropertyId(propertyId)).vehicles;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property vehicles", error.statusCode);
    }
  }

  async getPetsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getPetsByPropertyId(propertyId)).pets;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property pets", error.statusCode);
    }
  }

  async getTagsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getTagsByPropertyId(propertyId)).tags;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property tags", error.statusCode);
    }
  }

  async getParkingSpotsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getParkingSpotsByPropertyId(propertyId)).parkingSpots;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property parking spots", error.statusCode);
    }
  }

  async getReservationsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getReservationsByPropertyId(propertyId)).reservations;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property reservations", error.statusCode);
    }
  }

  async getMaintenanceIssueReportsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getMaintenanceIssueReportsByPropertyId(propertyId))
        .maintenanceIssueReports;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property maintenance issue reports", error.statusCode);
    }
  }

  async getInfractionsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getInfractionsByPropertyId(propertyId)).infractions;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property infractions", error.statusCode);
    }
  }

  async getComplaintsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getComplaintsByPropertyId(propertyId)).complaints;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property complaints", error.statusCode);
    }
  }

  async getFinesByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getFinesByPropertyId(propertyId)).fines;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property fines", error.statusCode);
    }
  }

  async getMonthlyMaintenanceChargesByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getMonthlyMaintenanceChargesByPropertyId(propertyId))
        .monthlyMaintenanceCharges;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property monthly maintenance charges", error.statusCode);
    }
  }

  async getPropertyResidentsIdsByPropertyId(propertyId: string) {
    try {
      return (await this.propertyPersistenceService.getPropertyResidentsIdsByPropertyId(propertyId)).residents;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get Property residents push tokens", error.statusCode);
    }
  }
}
