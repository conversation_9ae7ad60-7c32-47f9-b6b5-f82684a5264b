import { HttpException, Injectable } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { MailService } from '../../mail/mail.service';
import { ErrorHandlerService } from '../../error-handler/error-handler.service';
import { User } from '@prisma/client';
import { UserResponseDto } from './dto/user-response.dto';
import { UserWithRoles } from '../../auth/auth.types';
import * as bcrypt from 'bcryptjs';
import { ConfirmPasswordDto } from '../../auth/dto/confir-password.dto';
import { UserPersistenceService } from 'src/modules/persistence/user-persistence.service';
import { MobileUserResponseDto } from 'src/modules/mobile/dto/mobile-user-response.dto';
import { ValidateTokenEmailDto } from 'src/modules/auth/dto/validations.dto';

@Injectable()
export class UserService {
  constructor(
    private readonly errorHandlerService: ErrorHandlerService,
    private readonly userPersistenceService: UserPersistenceService,
    private readonly mailService: MailService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
    try {
      const passwordConfirmationToken = Math.floor(100000 + Math.random() * 900000).toString();
      const dbUser = await this.userPersistenceService.findByEmail(createUserDto.email);
      if (dbUser) {
        throw new HttpException('User with this email already exists', 400);
      }
      const user = await this.userPersistenceService.createUser({ ...createUserDto, passwordConfirmationToken });
      return user;
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't create user", error.statusCode);
    }
  }

  async findAll(): Promise<UserResponseDto[]> {
    try {
      return await this.userPersistenceService.findAll();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all users", error.statusCode);
    }
  }

  async findAllForSelect() {
    try {
      return await this.userPersistenceService.findAllForSelect();
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get all users", error.statusCode);
    }
  }

  async findOne(id: User['id']): Promise<UserResponseDto> {
    try {
      return await this.userPersistenceService.findById(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get user", error.statusCode);
    }
  }

  async update(id: User['id'], updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
    try {
      return await this.userPersistenceService.updateUser(id, updateUserDto);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't update user", error.statusCode);
    }
  }

  async softDelete(id: User['id']): Promise<UserResponseDto> {
    try {
      return await this.userPersistenceService.softDelete(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't delete user", error.statusCode);
    }
  }

  async hardDelete(id: User['id']): Promise<UserResponseDto> {
    try {
      return await this.userPersistenceService.delete(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't delete completly user", error.statusCode);
    }
  }

  async findByEmail(email: User['email']): Promise<UserWithRoles> {
    try {
      return await this.userPersistenceService.findByEmail(email);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get user", error.statusCode);
    }
  }

  async findByPasswordConfirmationToken(token: string) {
    try {
      return await this.userPersistenceService.findByPasswordConfirmationToken(token);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't find user", error.statusCode);
    }
  }

  async savePassword(id: User['id'], password: User['password']) {
    try {
      return await this.userPersistenceService.savePassword(id, password);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't save password", error.statusCode);
    }
  }

  async confirmPassword(confirmPasswordDto: ConfirmPasswordDto) {
    const { token, password } = confirmPasswordDto;
    const user = await this.userPersistenceService.findByPasswordConfirmationToken(token);

    if (!user) {
      throw new Error('Invalid token');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    await this.userPersistenceService.savePassword(user.id, hashedPassword);

    return { message: 'Password confirmed successfully' };
  }

  async findOneForMobile(id: User['id']): Promise<MobileUserResponseDto> {
    try {
      return await this.userPersistenceService.findForMobileById(id);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get user", error.statusCode);
    }
  }

  async validateEmailAndSendUserToken(email: User['email']) {
    try {
      const user = await this.userPersistenceService.getDataByEmailForValidation(email);
      if (!user) {
        return { exists: false };
      }
      if (user.passwordConfirmed) {
        return { exists: true, confirmed: true };
      }
      const passwordConfirmationToken = Math.floor(100000 + Math.random() * 900000).toString();
      const userUpdated = await this.userPersistenceService.updatePasswordConfirmationToken(
        user.id,
        passwordConfirmationToken,
      );
      this.mailService.sendUserToken(userUpdated.email, userUpdated.passwordConfirmationToken);
      return { exists: true, confirmed: false };
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get user", error.statusCode);
    }
  }

  async validateToken(validateTokenAndEmailDto: ValidateTokenEmailDto): Promise<{ valid: boolean }> {
    try {
      const user = await this.userPersistenceService.findByPasswordConfirmationTokenAndEmail(validateTokenAndEmailDto);
      if (!user) {
        return { valid: false };
      }
      return { valid: true };
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get user", error.statusCode);
    }
  }

  async getUserByTokenAndEmail(validateTokenAndEmailDto: ValidateTokenEmailDto) {
    try {
      return await this.userPersistenceService.findByPasswordConfirmationTokenAndEmail(validateTokenAndEmailDto);
    } catch (error: any) {
      this.errorHandlerService.handleError(error, "Can't get user", error.statusCode);
    }
  }
}
