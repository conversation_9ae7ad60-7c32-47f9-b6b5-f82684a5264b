import { Inject, Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import configuration from 'src/config/configuration';
import { ConfigType } from '@nestjs/config';

@Injectable()
export class MailService {
  private readonly transporter: nodemailer.Transporter;

  constructor(
    @Inject(configuration.KEY)
    private readonly config: ConfigType<typeof configuration>,
  ) {
    this.transporter = nodemailer.createTransport({
      host: this.config.emailProvider.host,
      port: 2525,
      secure: false, // true for 465, false for other ports
      auth: {
        user: this.config.emailProvider.authUser, // your email
        pass: this.config.emailProvider.authPassword, // your email password
      },
    });
  }

  async sendUserToken(email: string, token: string) {
    try {
      await this.transporter.sendMail({
        from: '"Your App" <<EMAIL>>', // sender address
        to: email, // list of receivers
        subject: 'Activación de cuenta', // Subject line
        html: `<b>Tu token es ${token}</b>`, // html body
      });
    } catch (error) {
      console.log(error);
    }
  }

  async send(notification: Notification) {
    console.log(notification);
  }
}
