import { registerAs } from '@nestjs/config';

export interface Config {
  serverPort: number;
  appName: string;
  environment: string;
  apiKey: string;
  corsOrigin: string;
  jwt: JWT;
  emailProvider: EmailProvider;
  cloudinary: Cloudinary;
}

interface JWT {
  token: string;
}

interface EmailProvider {
  host: string;
  authUser: string;
  authPassword: string;
  port: number;
}

interface Cloudinary {
  cloudName: string;
  apiKey: string;
  apiSecret: string;
}

export default registerAs(
  'configuration',
  (): Config => ({
    serverPort: parseInt(process.env.SERVER_PORT) || 3000,
    appName: process.env.APP_NAME,
    environment: process.env.NODE_ENV,
    apiKey: process.env.API_KEY,
    corsOrigin: process.env.CORS_ORIGIN,
    jwt: { token: process.env.JWT_TOKENs },
    emailProvider: {
      host: process.env.EMAIL_HOST,
      authUser: process.env.EMAIL_AUTH_USER,
      authPassword: process.env.EMAIL_AUTH_PASSWORD,
      port: Number(process.env.EMAIl_PORT),
    },
    cloudinary: {
      cloudName: process.env.CLOUDINARY_CLOUD_NAME,
      apiKey: process.env.CLOUDINARY_API_KEY,
      apiSecret: process.env.CLOUDINARY_API_SECRET,
    },
  }),
);
