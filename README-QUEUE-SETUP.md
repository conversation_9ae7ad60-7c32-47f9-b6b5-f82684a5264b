# 🚀 Setup del Sistema de Colas BullMQ

Este documento te guía para configurar el nuevo sistema de colas BullMQ que reemplaza el procesamiento síncrono de push notifications.

## ✅ Estado Actual

- **Sistema completamente funcional** y libre de errores
- **Tests unitarios pasando** (12/12 tests exitosos)
- **Build sin errores TypeScript**
- **Documentación actualizada**

## 📋 Requisitos Previos

- **Docker** y **Docker Compose** instalados
- **Node.js** (versión 18 o superior)
- **npm** o **yarn**

## 🛠️ Instalación Automática

### Opción 1: Script Automático (Recomendado)

```bash
# Ejecutar el script de setup automático
./scripts/setup-queue-system.sh
```

Este script:

- ✅ Verifica dependencias
- ✅ Configura variables de entorno
- ✅ Levanta Redis y PostgreSQL
- ✅ Instala dependencias de Node.js
- ✅ Ejecuta tests básicos
- ✅ Verifica que todo funcione

### Opción 2: Setup Manual

#### 1. Configurar Variables de Entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env

# Editar .env y agregar/verificar estas variables:
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

#### 2. Levantar Servicios

```bash
# Levantar Redis y PostgreSQL
docker-compose up -d postgres redis

# Verificar que Redis esté funcionando
docker exec hoams_redis redis-cli ping
# Debería responder: PONG
```

#### 3. Instalar Dependencias

```bash
# Instalar dependencias de Node.js
npm install

# Verificar dependencias específicas de BullMQ
npm list bullmq @nestjs/bullmq ioredis @nestjs/schedule
```

#### 4. Ejecutar Tests

```bash
# Ejecutar tests del sistema de colas
npm test -- --testPathPattern="push-notifications" --verbose
```

## 🔧 Verificación del Setup

### 1. Verificar Redis

```bash
# Conectar a Redis CLI
docker exec -it hoams_redis redis-cli

# Dentro de Redis CLI:
redis> ping
PONG
redis> exit
```

### 2. Verificar Servicios Docker

```bash
# Ver estado de contenedores
docker-compose ps

# Debería mostrar:
# hoams_postgres - Up
# hoams_redis    - Up
```

### 3. Verificar Logs

```bash
# Ver logs de Redis
docker-compose logs redis

# Ver logs de PostgreSQL
docker-compose logs postgres
```

## 🚀 Iniciar la Aplicación

```bash
# Modo desarrollo
npm run start:dev

# Modo producción
npm run build
npm run start:prod
```

## 📊 Monitoreo de Colas

Una vez que la aplicación esté ejecutándose:

### Endpoints de Monitoreo

```bash
# Estadísticas básicas de la cola
curl http://localhost:3000/push-notifications/queue/stats

# Métricas detalladas
curl http://localhost:3000/push-notifications/queue/metrics

# Información de un job específico
curl http://localhost:3000/push-notifications/queue/job/{jobId}
```

### Gestión de Colas

```bash
# Pausar procesamiento
curl -X POST http://localhost:3000/push-notifications/queue/pause

# Reanudar procesamiento
curl -X POST http://localhost:3000/push-notifications/queue/resume

# Limpiar jobs antiguos
curl -X DELETE http://localhost:3000/push-notifications/queue/clean

# Reintentar jobs fallidos
curl -X POST http://localhost:3000/push-notifications/queue/retry
```

## 🐛 Troubleshooting

### Problema: Redis no se conecta

```bash
# Verificar que Redis esté ejecutándose
docker ps | grep redis

# Reiniciar Redis
docker-compose restart redis

# Ver logs de Redis
docker-compose logs redis
```

### Problema: Tests fallan

```bash
# Verificar que Redis esté disponible
docker exec hoams_redis redis-cli ping

# Verificar variables de entorno
cat .env | grep REDIS

# Ejecutar tests con más detalle
npm test -- --testPathPattern="push-notifications" --verbose
```

### Problema: Dependencias faltantes

```bash
# Reinstalar dependencias
rm -rf node_modules package-lock.json
npm install

# Verificar dependencias específicas
npm list bullmq @nestjs/bullmq ioredis @nestjs/schedule
```

### Problema: Puerto ocupado

```bash
# Verificar qué está usando el puerto 6379
lsof -i :6379

# Cambiar puerto en docker-compose.yml si es necesario
# redis:
#   ports:
#     - '6380:6379'  # Usar puerto 6380 en lugar de 6379
```

## 📚 Documentación Adicional

- **Documentación completa**: `docs/push-notifications-queue-migration.md`
- **Arquitectura del sistema**: Ver sección "Componentes Principales" en la documentación
- **API Reference**: Swagger UI en `http://localhost:3000/api` cuando la app esté ejecutándose

## 🔄 Comandos Útiles

```bash
# Parar todos los servicios
docker-compose down

# Parar y eliminar volúmenes (CUIDADO: elimina datos)
docker-compose down -v

# Ver logs en tiempo real
docker-compose logs -f redis

# Conectar a Redis CLI
docker exec -it hoams_redis redis-cli

# Monitorear Redis en tiempo real
docker exec -it hoams_redis redis-cli monitor

# Verificar memoria de Redis
docker exec hoams_redis redis-cli info memory
```

## ✅ Checklist de Verificación

- [ ] Docker y Docker Compose instalados
- [ ] Archivo `.env` configurado con variables de Redis
- [ ] Servicios `postgres` y `redis` ejecutándose
- [ ] Redis responde a `ping`
- [ ] Dependencias de Node.js instaladas
- [ ] Tests del sistema de colas pasan
- [ ] Aplicación inicia sin errores
- [ ] Endpoints de monitoreo responden

## 🆘 Soporte

Si tienes problemas:

1. Revisa los logs: `docker-compose logs redis`
2. Verifica la conectividad: `docker exec hoams_redis redis-cli ping`
3. Consulta la documentación completa en `docs/push-notifications-queue-migration.md`
4. Ejecuta el script de setup nuevamente: `./scripts/setup-queue-system.sh`
