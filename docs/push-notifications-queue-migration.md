# Migración del Sistema de Push Notifications a BullMQ

## Resumen

Este documento describe la migración del sistema de push notifications de un procesamiento síncrono directo a una arquitectura basada en colas usando BullMQ y Redis. Esta migración mejora la resilencia del sistema y permite la recuperación de procesos en caso de fallos.

## Arquitectura Anterior vs Nueva

### Arquitectura Anterior (Síncrona)

```
Cliente → Controller → Service → Expo SDK → Respuesta
```

### Nueva Arquitectura (Asíncrona con Colas)

```
Cliente → Controller → QueueService → Redis Queue
                                   ↓
Processor → Expo SDK → Resultado almacenado
```

## Componentes Principales

### 1. NotificationQueueService

- **Ubicación**: `src/modules/business-logic/push-notifications/services/notification-queue.service.ts`
- **Responsabilidad**: Gestión de colas BullMQ, agregar jobs y monitoreo básico
- **Métodos principales**:
  - `addSingleNotification()`: Agregar notificación individual
  - `addBulkNotification()`: Agregar notificaciones masivas
  - `addCleanupJob()`: Agregar job de limpieza
  - `getQueueStats()`: Obtener estadísticas de la cola

### 2. NotificationProcessor

- **Ubicación**: `src/modules/business-logic/push-notifications/processors/notification.processor.ts`
- **Responsabilidad**: Procesamiento real de jobs usando Expo SDK
- **Características**:
  - Procesamiento por lotes para notificaciones masivas
  - Manejo de errores y tokens inválidos
  - Actualización de progreso en tiempo real
  - Retry automático con backoff exponencial

### 3. QueueMonitoringService

- **Ubicación**: `src/modules/business-logic/push-notifications/services/queue-monitoring.service.ts`
- **Responsabilidad**: Monitoreo avanzado y gestión de colas
- **Características**:
  - Verificación automática de salud cada 5 minutos
  - Limpieza automática de jobs cada hora
  - Métricas detalladas de rendimiento
  - Gestión manual de jobs (pausar, reanudar, reintentar)

### 4. PushNotificationsService (Refactorizado)

- **Ubicación**: `src/modules/business-logic/push-notifications/push-notifications.service.ts`
- **Cambios principales**:
  - Métodos ahora retornan `QueueJobResponseDto` en lugar de procesar directamente
  - Eliminación de lógica de procesamiento Expo SDK
  - Agregación de métodos de monitoreo de cola

## Configuración

### Variables de Entorno

```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_jwt_secret
```

### Configuración de Cola

```typescript
// En push-notifications.module.ts
BullModule.registerQueue({
  name: 'push-notifications',
  defaultJobOptions: {
    removeOnComplete: 100, // Mantener 100 jobs completados
    removeOnFail: 50, // Mantener 50 jobs fallidos
    attempts: 3, // 3 intentos por job
    backoff: {
      type: 'exponential',
      delay: 2000, // Delay inicial de 2 segundos
    },
  },
});
```

## Tipos de Jobs

### 1. Single Notification Job

```typescript
interface SingleNotificationJob {
  type: 'single';
  tokens: string[];
  title: string;
  body: string;
  data?: Record<string, any>;
  priority: JobPriority;
}
```

### 2. Bulk Notification Job

```typescript
interface BulkNotificationJob {
  type: 'bulk';
  notifications: Array<{
    tokens: string[];
    title: string;
    body: string;
    data?: Record<string, any>;
  }>;
  batchSize: number;
  priority: JobPriority;
}
```

### 3. Cleanup Job

```typescript
interface CleanupJob {
  type: 'cleanup';
  daysOld: number;
  priority: JobPriority;
}
```

## Nuevos Endpoints de API

### Endpoints de Cola

- `GET /push-notifications/queue/stats` - Estadísticas básicas de la cola
- `GET /push-notifications/queue/metrics` - Métricas detalladas
- `GET /push-notifications/queue/job/:jobId` - Información de job específico
- `POST /push-notifications/queue/pause` - Pausar procesamiento
- `POST /push-notifications/queue/resume` - Reanudar procesamiento
- `DELETE /push-notifications/queue/clean` - Limpiar jobs antiguos
- `POST /push-notifications/queue/retry` - Reintentar jobs fallidos

### Cambios en Endpoints Existentes

- `POST /push-notifications/send` - Ahora retorna `QueueJobResponseDto`
- `POST /push-notifications/send-bulk` - Ahora retorna `QueueJobResponseDto`
- `DELETE /push-notifications/cleanup` - Ahora retorna `QueueJobResponseDto`

## Monitoreo y Alertas

### Verificaciones Automáticas (cada 5 minutos)

- Estado de la cola (pausada/activa)
- Número de jobs fallidos (alerta si > 50)
- Acumulación de jobs (alerta si > 1000 en espera)
- Conectividad con Redis

### Limpieza Automática (cada hora)

- Eliminación de jobs completados antiguos
- Eliminación de jobs fallidos antiguos
- Optimización de memoria Redis

### Métricas Disponibles

- Tasa de éxito de notificaciones
- Tiempo promedio de procesamiento
- Distribución de errores
- Rendimiento por tipo de job

## Estrategias de Retry

### Configuración de Reintentos

- **Intentos máximos**: 3 por job
- **Backoff**: Exponencial con delay inicial de 2 segundos
- **Delays**: 2s, 4s, 8s

### Manejo de Errores

- **Tokens inválidos**: Eliminación automática de la base de datos
- **Errores temporales**: Reintento automático
- **Errores permanentes**: Marcado como fallido después de 3 intentos

## Migración y Compatibilidad

### Cambios Breaking

- Los métodos de notificación ahora retornan `QueueJobResponseDto` en lugar de `NotificationResponseDto`
- El procesamiento es asíncrono, no hay respuesta inmediata del resultado

### Compatibilidad

- Los DTOs de entrada permanecen iguales
- Los endpoints mantienen las mismas rutas
- La autenticación JWT se mantiene igual

## Beneficios de la Migración

### Resilencia

- **Recuperación de fallos**: Jobs se procesan aunque el servicio se reinicie
- **Persistencia**: Jobs se almacenan en Redis hasta completarse
- **Retry automático**: Manejo inteligente de errores temporales

### Rendimiento

- **Procesamiento asíncrono**: Respuestas inmediatas al cliente
- **Procesamiento por lotes**: Optimización para notificaciones masivas
- **Escalabilidad**: Múltiples workers pueden procesar la misma cola

### Monitoreo

- **Visibilidad completa**: Estado de todos los jobs en tiempo real
- **Métricas detalladas**: Análisis de rendimiento y errores
- **Alertas automáticas**: Detección proactiva de problemas

## Estado Actual

✅ **Completado**:

- Sistema de colas BullMQ implementado y funcional
- Tests unitarios completos (12/12 tests pasando)
- Corrección de errores TypeScript (6 errores → 0 errores)
- Documentación técnica completa
- Scripts de setup automatizado

### Correcciones Realizadas

**TypeScript Fixes**:

- Corregidos tipos de parámetros en `NotificationProcessor`
- Eliminadas aserciones de tipo innecesarias
- Compatibilidad completa con BullMQ Job types

**Test Fixes**:

- Agregados métodos faltantes en mocks de BullMQ
- Corregida estructura de datos de prueba
- Implementada inyección manual de cola mock
- Ajustados valores esperados y mensajes de log

## Próximos Pasos

1. **Documentación de API**: Actualizar Swagger con nuevos endpoints
2. **Monitoreo avanzado**: Integrar con sistemas de métricas externos (Prometheus/Grafana)
3. **Dashboard**: Crear interfaz web para monitoreo visual de colas
4. **Escalabilidad**: Configurar múltiples workers para alta carga
5. **Tests de integración**: Agregar tests end-to-end con Redis real

## Troubleshooting

### Problemas Comunes

#### Cola no procesa jobs

- Verificar conexión Redis
- Verificar que el processor esté registrado
- Revisar logs de errores

#### Jobs fallan constantemente

- Verificar configuración Expo SDK
- Revisar tokens de notificación
- Verificar conectividad externa

#### Acumulación de jobs

- Verificar rendimiento del processor
- Considerar aumentar workers
- Revisar configuración de batch size

### Comandos Útiles

```bash
# Verificar estado de Redis
redis-cli ping

# Monitorear cola en tiempo real
redis-cli monitor

# Limpiar todas las colas (CUIDADO)
redis-cli flushdb
```
