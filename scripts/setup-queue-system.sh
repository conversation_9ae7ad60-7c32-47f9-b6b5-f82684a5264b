#!/bin/bash

# Script para configurar el sistema de colas BullMQ
echo "🚀 Configurando el sistema de colas BullMQ..."

# Verificar si Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker no está instalado. Por favor instala Docker primero."
    exit 1
fi

# Verificar si Docker Compose está instalado
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose no está instalado. Por favor instala Docker Compose primero."
    exit 1
fi

# Crear archivo .env si no existe
if [ ! -f .env ]; then
    echo "📝 Creando archivo .env desde .env.example..."
    cp .env.example .env
    echo "✅ Archivo .env creado. Por favor configura las variables necesarias."
else
    echo "✅ Archivo .env ya existe."
fi

# Verificar si las variables de Redis están en .env
if ! grep -q "REDIS_HOST" .env; then
    echo "📝 Agregando variables de Redis al archivo .env..."
    echo "" >> .env
    echo "# Redis Configuration (for BullMQ queues)" >> .env
    echo "REDIS_HOST=localhost" >> .env
    echo "REDIS_PORT=6379" >> .env
    echo "REDIS_PASSWORD=" >> .env
    echo "REDIS_DB=0" >> .env
    echo "✅ Variables de Redis agregadas al .env"
fi

# Levantar servicios de Docker
echo "🐳 Levantando servicios de Docker (PostgreSQL y Redis)..."
docker-compose up -d postgres redis

# Esperar a que Redis esté listo
echo "⏳ Esperando a que Redis esté listo..."
timeout=30
counter=0
while ! docker exec hoams_redis redis-cli ping > /dev/null 2>&1; do
    if [ $counter -eq $timeout ]; then
        echo "❌ Timeout esperando a Redis. Verifica los logs: docker-compose logs redis"
        exit 1
    fi
    sleep 1
    counter=$((counter + 1))
done

echo "✅ Redis está listo!"

# Verificar conexión a Redis
echo "🔍 Verificando conexión a Redis..."
if docker exec hoams_redis redis-cli ping | grep -q "PONG"; then
    echo "✅ Conexión a Redis exitosa!"
else
    echo "❌ Error conectando a Redis"
    exit 1
fi

# Instalar dependencias si no están instaladas
if [ ! -d "node_modules" ]; then
    echo "📦 Instalando dependencias de Node.js..."
    npm install
fi

# Verificar que las dependencias de BullMQ estén instaladas
echo "🔍 Verificando dependencias de BullMQ..."
if npm list bullmq @nestjs/bullmq ioredis @nestjs/schedule > /dev/null 2>&1; then
    echo "✅ Todas las dependencias de BullMQ están instaladas!"
else
    echo "❌ Faltan dependencias de BullMQ. Instalando..."
    npm install bullmq @nestjs/bullmq ioredis @nestjs/schedule
fi

# Ejecutar tests del sistema de colas
echo "🧪 Ejecutando tests del sistema de colas..."
if npm test -- --testPathPattern=notification-queue.service.spec.ts --silent; then
    echo "✅ Tests del sistema de colas pasaron exitosamente!"
else
    echo "⚠️  Algunos tests fallaron, pero el sistema debería funcionar. Revisa los logs para más detalles."
fi

echo ""
echo "🎉 ¡Setup completado!"
echo ""
echo "📋 Resumen de servicios:"
echo "  - PostgreSQL: localhost:5433"
echo "  - Redis: localhost:6379"
echo ""
echo "🔧 Comandos útiles:"
echo "  - Ver logs de Redis: docker-compose logs redis"
echo "  - Conectar a Redis CLI: docker exec -it hoams_redis redis-cli"
echo "  - Parar servicios: docker-compose down"
echo "  - Ver estado de colas: curl http://localhost:3000/push-notifications/queue/stats"
echo ""
echo "📚 Documentación completa en: docs/push-notifications-queue-migration.md"
echo ""
echo "🚀 Para iniciar la aplicación: npm run start:dev"
