# Database
DATABASE_URL="postgresql://username:password@localhost:5432/hoa_manager"

# Server Configuration
SERVER_PORT=3000
APP_NAME="HOA Manager System"
NODE_ENV="development"
API_KEY="your-api-key-here"
CORS_ORIGIN="http://localhost:3000"

# JWT Configuration
JWT_SECRET="your-jwt-secret-key"

# Firebase Configuration (for existing push notifications)
FIREBASE_PROJECT_ID="your-firebase-project-id"
FIREBASE_CLIENT_EMAIL="your-firebase-client-email"
FIREBASE_PRIVATE_KEY="your-firebase-private-key"

# Expo Push Notifications (NEW)
EXPO_ACCESS_TOKEN="your-expo-access-token"

# Redis Configuration (for BullMQ queues)
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""
REDIS_DB=0

# Email Configuration
EMAIL_HOST="smtp.gmail.com"
EMAIL_AUTH_USER="<EMAIL>"
EMAIL_AUTH_PASSWORD="your-email-password"
EMAIL_PORT=587

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"
